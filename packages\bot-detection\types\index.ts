export interface ViewData {
  viewsPerMinute: number
  engagementRate: number
  accountAge: number
  consistencyScore: number
}

export interface BotDetectionResult {
  score: number
  level: "safe" | "warning" | "suspicious" | "bot"
  details: {
    velocityScore: number
    engagementScore: number
    accountScore: number
    patternScore: number
  }
}

export interface PromotionAnalysis {
  promotionId: string
  botScore: number
  level: string
  timestamp: Date
  recommendations: string[]
}

export interface SocialMediaMetrics {
  platform: 'tiktok' | 'instagram' | 'youtube'
  postId: string
  postUrl: string
  views: number
  likes: number
  comments: number
  shares: number
  engagementRate: number
  timestamp: string
  username: string
  accountInfo: {
    followerCount: number
    accountAge: number
    verificationStatus: boolean
  }
}

export interface FraudDetectionResult {
  fraudScore: number
  fraudLevel: 'safe' | 'warning' | 'suspicious' | 'fraud'
  flags: string[]
  payoutEligible: boolean
  payoutMultiplier: number
  payoutDelay: number // hours, -1 means never
  recommendation: string
  details: {
    engagement: { score: number; flags: string[] }
    ratios: { score: number; flags: string[] }
    account: { score: number; flags: string[] }
    temporal: { score: number; flags: string[] }
    consistency: { score: number; flags: string[] }
  }
}

export interface PayoutCalculation {
  originalAmount: number
  adjustedAmount: number
  deductions: Array<{ reason: string; amount: number }>
  finalAmount: number
}

export interface BotDetectionThreshold {
  id: string
  platform: 'tiktok' | 'instagram' | 'youtube'
  followerRangeMin: number
  followerRangeMax: number | null
  minEngagementRate: number
  maxEngagementRate: number
  suspiciousGrowthRate: number
  commentQualityThreshold: number
  viewToLikeRatioMin: number | null
  viewToLikeRatioMax: number | null
  isActive: boolean
  createdAt: string
  updatedAt: string
}
