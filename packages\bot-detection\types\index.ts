export interface ViewData {
  viewsPerMinute: number
  engagementRate: number
  accountAge: number
  consistencyScore: number
}

export interface BotDetectionResult {
  score: number
  level: "safe" | "warning" | "suspicious" | "bot"
  details: {
    velocityScore: number
    engagementScore: number
    accountScore: number
    patternScore: number
  }
}

export interface PromotionAnalysis {
  promotionId: string
  botScore: number
  level: string
  timestamp: Date
  recommendations: string[]
}
