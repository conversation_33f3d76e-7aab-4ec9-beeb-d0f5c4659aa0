"use client"

import { createClient } from "@supabase/supabase-js"
import { useEffect, useState } from "react"
import { useAuth } from "./use-auth"

interface SocialMediaAccount {
  id: string
  user_id: string
  platform: 'tiktok' | 'instagram' | 'youtube' | 'google'
  platform_user_id: string
  username: string
  display_name: string | null
  profile_picture_url: string | null
  follower_count: number
  access_token: string
  refresh_token: string | null
  token_expires_at: string | null
  is_active: boolean
  last_synced_at: string
  created_at: string
  updated_at: string
}

interface EngagementData {
  id: string
  submission_id: string
  social_account_id: string
  post_url: string
  platform_post_id: string
  views_count: number
  likes_count: number
  comments_count: number
  shares_count: number
  engagement_rate: number
  bot_detection_score: number
  is_suspicious: boolean
  fraud_flags: string[]
  last_checked_at: string
  created_at: string
  updated_at: string
}

interface BotDetectionThreshold {
  id: string
  platform: 'tiktok' | 'instagram' | 'youtube' | 'google'
  follower_range_min: number
  follower_range_max: number | null
  min_engagement_rate: number
  max_engagement_rate: number
  suspicious_growth_rate: number
  comment_quality_threshold: number
  view_to_like_ratio_min: number | null
  view_to_like_ratio_max: number | null
  is_active: boolean
}

export function useSocialMedia() {
  const { user } = useAuth()
  const [socialAccounts, setSocialAccounts] = useState<SocialMediaAccount[]>([])
  const [loading, setLoading] = useState(true)
  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  useEffect(() => {
    if (user) {
      fetchSocialAccounts()
    }
  }, [user])

  const fetchSocialAccounts = async () => {
    if (!user) return

    const { data, error } = await supabase
      .from('social_media_accounts')
      .select('*')
      .eq('user_id', user.id)
      .eq('is_active', true)
      .order('created_at', { ascending: false })

    if (!error && data) {
      setSocialAccounts(data)
    }
    setLoading(false)
  }

  const connectSocialAccount = async (platform: string) => {
    // This would trigger OAuth flow
    const { error } = await supabase.auth.signInWithOAuth({
      provider: platform as any,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_AUTH_URL}/auth/callback`,
        scopes: getScopesForPlatform(platform),
      },
    })

    return { error }
  }

  const disconnectSocialAccount = async (accountId: string) => {
    const { error } = await supabase
      .from('social_media_accounts')
      .update({ is_active: false })
      .eq('id', accountId)
      .eq('user_id', user?.id)

    if (!error) {
      await fetchSocialAccounts()
    }

    return { error }
  }

  const getEngagementData = async (submissionId: string) => {
    const { data, error } = await supabase
      .from('engagement_tracking')
      .select(`
        *,
        social_media_accounts (
          platform,
          username,
          follower_count
        )
      `)
      .eq('submission_id', submissionId)
      .order('created_at', { ascending: false })

    return { data, error }
  }

  const calculateBotDetectionScore = async (
    platform: string,
    followerCount: number,
    engagementMetrics: {
      views: number
      likes: number
      comments: number
      shares: number
    }
  ) => {
    // Get thresholds for platform and follower range
    const { data: thresholds } = await supabase
      .from('bot_detection_thresholds')
      .select('*')
      .eq('platform', platform)
      .eq('is_active', true)
      .lte('follower_range_min', followerCount)
      .or(`follower_range_max.is.null,follower_range_max.gte.${followerCount}`)
      .single()

    if (!thresholds) {
      return { score: 0, flags: ['no_thresholds_found'] }
    }

    const flags: string[] = []
    let suspiciousScore = 0

    // Calculate engagement rate
    const engagementRate = (engagementMetrics.likes + engagementMetrics.comments + engagementMetrics.shares) / engagementMetrics.views

    // Check engagement rate bounds
    if (engagementRate < thresholds.min_engagement_rate) {
      flags.push('low_engagement_rate')
      suspiciousScore += 0.3
    } else if (engagementRate > thresholds.max_engagement_rate) {
      flags.push('high_engagement_rate')
      suspiciousScore += 0.4
    }

    // Check view to like ratio
    if (thresholds.view_to_like_ratio_min && thresholds.view_to_like_ratio_max) {
      const viewToLikeRatio = engagementMetrics.views / (engagementMetrics.likes || 1)
      if (viewToLikeRatio < thresholds.view_to_like_ratio_min || viewToLikeRatio > thresholds.view_to_like_ratio_max) {
        flags.push('suspicious_view_like_ratio')
        suspiciousScore += 0.3
      }
    }

    // Additional checks can be added here
    // - Comment quality analysis
    // - Growth rate analysis
    // - Timing pattern analysis

    return {
      score: Math.min(suspiciousScore, 1.0),
      flags,
      isSuspicious: suspiciousScore > 0.5
    }
  }

  const updateEngagementData = async (
    submissionId: string,
    socialAccountId: string,
    postUrl: string,
    platformPostId: string,
    metrics: {
      views: number
      likes: number
      comments: number
      shares: number
    }
  ) => {
    // Get social account info for bot detection
    const { data: socialAccount } = await supabase
      .from('social_media_accounts')
      .select('platform, follower_count')
      .eq('id', socialAccountId)
      .single()

    if (!socialAccount) {
      return { error: 'Social account not found' }
    }

    // Calculate bot detection score
    const botDetection = await calculateBotDetectionScore(
      socialAccount.platform,
      socialAccount.follower_count,
      metrics
    )

    const engagementRate = (metrics.likes + metrics.comments + metrics.shares) / metrics.views

    // Insert or update engagement tracking
    const { data, error } = await supabase
      .from('engagement_tracking')
      .upsert({
        submission_id: submissionId,
        social_account_id: socialAccountId,
        post_url: postUrl,
        platform_post_id: platformPostId,
        views_count: metrics.views,
        likes_count: metrics.likes,
        comments_count: metrics.comments,
        shares_count: metrics.shares,
        engagement_rate: engagementRate,
        bot_detection_score: botDetection.score,
        is_suspicious: botDetection.isSuspicious,
        fraud_flags: botDetection.flags,
        last_checked_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'submission_id,platform_post_id'
      })

    return { data, error, botDetection }
  }

  return {
    socialAccounts,
    loading,
    connectSocialAccount,
    disconnectSocialAccount,
    getEngagementData,
    updateEngagementData,
    calculateBotDetectionScore,
    refreshAccounts: fetchSocialAccounts,
  }
}

function getScopesForPlatform(platform: string): string {
  switch (platform) {
    case 'tiktok':
      return 'user.info.basic,video.list,video.upload'
    case 'instagram':
      return 'user_profile,user_media'
    case 'youtube':
      return 'https://www.googleapis.com/auth/youtube.readonly'
    default:
      return ''
  }
}
