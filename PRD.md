# Product Requirements Document: Creator Booster

## 1. <PERSON><PERSON> & <PERSON><PERSON>an <PERSON>

**Visi:** Menjadi platform penghubung utama antara kreator konten yang ingin meningkatkan jangkauan dan para promotor (editor/marketer) yang ingin mendapatkan penghasilan dengan keahlian mereka dalam mengolah dan menyebarkan konten.

**Ringkasan:** Creator Booster adalah platform marketplace di mana kreator konten dapat meluncurkan kampanye promosi dengan budget tertentu. Promotor dapat bergabung dengan kampanye, mengunduh materi konten yang disediakan, mengeditnya secara kreatif, dan mengunggahnya ke platform media sosial seperti TikTok atau Instagram. Platform akan melacak kinerja konten tersebut (terutama jumlah tayangan/views) dan memberikan kompensasi kepada promotor sesuai dengan tarif yang telah ditentukan oleh kreator.

## 2. Arsitektur & Teknologi

Perubahan dari rencana awal, kita akan menggunakan ekosistem Vercel dan Supabase untuk kemudahan pengembangan dan deployment.

*   **Monorepo:** Menggunakan Turborepo (struktur yang sudah ada saat ini).
*   **Framework:** Next.js & Tailwind CSS.
*   **Hosting:** Vercel. Setiap aplikasi dalam direktori `/apps` akan di-deploy sebagai project terpisah di Vercel dengan subdomainnya masing-masing.
*   **Database:** Supabase (PostgreSQL).
*   **Autentikasi:** Supabase Auth (termasuk login sosial media).
*   **Penyimpanan File (Bucket):** Supabase Storage untuk menyimpan materi konten dari kreator.
*   **Backend Logic (Functions):** Supabase Edge Functions untuk proses backend seperti kalkulasi payout, cron jobs, dan deteksi bot.
*   **Email Transaksional:** Resend (untuk notifikasi, reset password, dll).
*   **Pembayaran/Payout:** Integrasi dengan payment gateway yang mendukung payout seperti Stripe Connect atau Xendit (untuk pasar Indonesia).

## 3. Struktur Direktori & Subdomain

Struktur monorepo yang ada saat ini sudah sangat sesuai. Kita akan memetakannya sebagai berikut:

*   `apps/landing` -> `creatorbooster.com` (Domain utama)
*   `apps/auth` -> `auth.creatorbooster.com` (Untuk login dan registrasi)
*   `apps/dashboard` -> `dashboard.creatorbooster.com` (Untuk kreator dan promotor)
*   `apps/admin` -> `admin.creatorbooster.com` (Untuk manajemen platform)

## 4. Fitur Inti

### 4.1. Fitur untuk Kreator Konten
*   **Manajemen Kampanye:** Membuat, mengedit, dan memantau kampanye.
*   **Pendanaan:** Menentukan total budget kampanye dan harga per view (misal: Rp 10 per view).
*   **Penyediaan Materi:** Mengunggah materi video/gambar melalui link (Google Drive, YouTube) atau upload langsung ke Supabase Storage.
*   **Persyaratan:** Menulis syarat & ketentuan untuk promotor (misal: wajib menggunakan sound tertentu, durasi minimal, dll).
*   **Dashboard Analitik:** Melihat total views yang dihasilkan, sisa budget, dan promotor teratas.

### 4.2. Fitur untuk Promotor
*   **Pendaftaran & Profil:** Mendaftar dan melengkapi profil.
*   **Menautkan Akun Media Sosial:** Menghubungkan akun media sosial (TikTok, Instagram, YouTube) melalui proses OAuth untuk otorisasi pengambilan data.
*   **Marketplace Kampanye:** Menjelajahi dan memilih kampanye yang tersedia.
*   **Proses Promosi:**
    1.  Bergabung dengan kampanye.
    2.  Mengunduh materi.
    3.  Mengedit dan mengunggah ke akun media sosialnya yang sudah terhubung.
    4.  Menyalin link postingan dan men-submit-nya ke platform Creator Booster untuk tracking.
*   **Dashboard Pendapatan:** Melihat total views, estimasi pendapatan, dan riwayat payout.

### 4.3. Fitur untuk Admin
*   **Manajemen Pengguna:** Melihat, memblokir, atau menghapus pengguna (kreator/promotor).
*   **Manajemen Keuangan:** Memantau dana masuk dari kreator, mengelola permintaan withdrawal, dan melakukan payout.
*   **Persetujuan & Keluhan:** Meninjau kampanye yang ditandai (flagged) atau laporan keluhan dari pengguna.
*   **Dashboard Global:** Melihat metrik keseluruhan platform.

## 5. Sistem Pelacakan View & Deteksi Bot (API-Based)

Ini adalah fitur krusial yang akan dibangun di atas API resmi untuk stabilitas dan akurasi. Pendekatan scraping akan dihindari.

### 5.1. Alur Pelacakan View via API
1.  **Otorisasi Akun:** Saat onboarding atau melalui dashboard, promotor akan menghubungkan akun media sosial mereka (TikTok, Instagram, YouTube) ke Creator Booster menggunakan alur otorisasi OAuth2.
2.  **Penyimpanan Token:** Platform akan secara aman menyimpan `access_token` dan `refresh_token` yang diberikan oleh penyedia OAuth untuk setiap promotor.
3.  **Submit Postingan:** Promotor men-submit URL postingan dari akun yang sudah terhubung.
4.  **Pengambilan Data Otomatis:** Sebuah **Supabase Edge Function** berjalan sebagai cron job (misalnya, setiap 30 menit). Fungsi ini akan:
    a. Mengambil daftar postingan yang aktif dilacak.
    b. Menggunakan `access_token` (dan me-refresh-nya jika perlu) yang sesuai untuk memanggil endpoint API resmi dari TikTok/Instagram/YouTube.
    c. Mengambil data metrik terbaru (views, likes, comments, shares, dll.) secara langsung dan andal.
5.  **Penyimpanan & Kalkulasi:** Data metrik disimpan dalam database Supabase dan diperbarui secara periodik. Payout dihitung setiap 24 jam berdasarkan total view baru yang valid.

### 5.2. Logika & Rumus Deteksi Bot

Meskipun data berasal dari API yang tepercaya, deteksi anomali tetap penting untuk mencegah penyalahgunaan (misalnya, promotor membeli view berkualitas rendah untuk postingannya). Logika ini akan menggunakan data yang akurat dari API.

**Data yang Dikumpulkan per Postingan (via API):**
*   `V`: Jumlah Views
*   `L`: Jumlah Likes
*   `C`: Jumlah Comments
*   `S`: Jumlah Shares (jika tersedia)
*   `T`: Waktu sejak postingan di-submit (dalam menit)

**Rumus & Heuristik:**

1.  **Engagement Rate (ER):**
    *   `ER = (L + C + S) / V`
    *   **Threshold:** Jika `ER < 0.5%`, tandai sebagai **Peringatan Rendah**. Engagement yang sangat rendah tetap menjadi indikator kuat adanya view berkualitas rendah.

2.  **View Velocity (VV):**
    *   `VV = V / T` (Rata-rata view per menit)
    *   **Threshold:** Jika akun promotor memiliki riwayat performa rendah namun tiba-tiba menunjukkan `VV` yang sangat tinggi (misal: > 1000 views/menit dalam jam pertama), tandai sebagai **Peringatan Sedang**. Ini menunjukkan pertumbuhan anomali yang perlu diselidiki.

**Sistem Peringatan & Tindakan:**

Setiap postingan akan memiliki `bot_score` (0-100).

*   **Peringatan Rendah:** `bot_score += 15`
*   **Peringatan Sedang:** `bot_score += 40`
*   **Peringatan Tinggi (jika beberapa flag terpicu):** `bot_score += 60`

**Level Threshold & Aksi:**

*   **`bot_score` > 85 (Keyakinan Anomali Tinggi):**
    *   **Aksi:** Payout untuk postingan tersebut secara otomatis **digagalkan**.
    *   Kirim notifikasi ke admin untuk peninjauan manual.
    *   Jika promotor yang sama melakukan ini berulang kali (misal: 3 kali), akunnya otomatis di-**ban**.

*   **`bot_score` 50-84 (Potensi Anomali):**
    *   **Aksi:** Kirim **peringatan** ke promotor melalui email (via Resend).
    *   Tandai postingan untuk **peninjauan manual** oleh admin, namun payout tidak langsung digagalkan. Admin yang akan memutuskan.

*   **`bot_score` < 50 (Aman):**
    *   **Aksi:** Tidak ada tindakan, proses berjalan normal.

## 6. Setup Deployment Vercel

1.  **Konfigurasi Proyek:** Di dashboard Vercel, impor Git repository.
2.  **Setup Monorepo:** Vercel akan mendeteksi `turbo.json`. Anda perlu mengkonfigurasi "Root Directory" untuk setiap proyek.
    *   Untuk `landing`, set Root Directory ke `apps/landing`.
    *   Untuk `auth`, set Root Directory ke `apps/auth`, dan seterusnya.
3.  **Domain:** Hubungkan domain utama ke proyek `landing`. Atur subdomain (`auth.*`, `dashboard.*`, `admin.*`) dan arahkan ke proyek Vercel yang sesuai.
4.  **Environment Variables:** Tambahkan semua kunci Supabase (`SUPABASE_URL`, `SUPABASE_ANON_KEY`) dan kunci Resend (`RESEND_API_KEY`) ke semua proyek Vercel yang membutuhkannya.