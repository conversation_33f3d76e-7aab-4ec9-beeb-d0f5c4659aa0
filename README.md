# CreatorBoost Platform

Platform untuk menghubungkan content creator dengan promoter melalui sistem pay-per-view yang transparan dan anti-bot.

## 🏗️ Struktur Project

\`\`\`
creatorboost-platform/
├── apps/
│   ├── landing/          # Landing page (creatorboost.com)
│   ├── auth/            # Authentication (auth.creatorboost.com)  
│   ├── dashboard/       # Main app (app.creatorboost.com)
│   └── admin/           # Admin panel (admin.creatorboost.com)
├── packages/
│   ├── ui/              # Shared UI components
│   ├── database/        # Database schemas & client
│   └── bot-detection/   # Bot detection engine
├── supabase/
│   ├── migrations/      # Database migrations
│   └── functions/       # Edge functions
└── docs/
    ├── PRD.md
    └── DEPLOYMENT.md
\`\`\`

## 🚀 Quick Start

1. **Clone repository**
   \`\`\`bash
   git clone <repository-url>
   cd creatorboost-platform
   \`\`\`

2. **Install dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Setup environment variables**
   \`\`\`bash
   cp .env.example .env.local
   # Fill in your Supabase and other API keys
   \`\`\`

4. **Setup database**
   \`\`\`bash
   npm run db:push
   \`\`\`

5. **Start development servers**
   \`\`\`bash
   npm run dev
   \`\`\`

## 📱 Applications

### Landing Page (Port 3000)
- Marketing website
- Pricing information
- Feature showcase

### Auth App (Port 3001)  
- User authentication
- Registration
- Password reset

### Dashboard App (Port 3002)
- Creator dashboard
- Promoter dashboard
- Campaign management

### Admin App (Port 3003)
- User management
- Bot detection monitoring
- Payout management
- Platform analytics

## 🔧 Development

### Adding New Features
1. Create feature in appropriate app
2. Add shared components to `packages/ui`
3. Add database queries to `packages/database`
4. Update bot detection if needed

### Database Changes
1. Create migration in `supabase/migrations/`
2. Run `npm run db:push`
3. Update types with `npm run db:generate`

### Deployment
Each app deploys separately to Vercel with custom domains:
- Landing: `creatorboost.com`
- Auth: `auth.creatorboost.com`
- Dashboard: `app.creatorboost.com`  
- Admin: `admin.creatorboost.com`

## 🤖 Bot Detection

The platform uses advanced bot detection with:
- Velocity analysis (40% weight)
- Engagement patterns (30% weight)  
- Account age scoring (20% weight)
- Interaction consistency (10% weight)

Thresholds:
- 0-30: Safe (Normal payout)
- 31-60: Warning (48h delay)
- 61-80: Suspicious (Manual review)
- 81-100: Bot (Account ban)

## 📊 Tech Stack

- **Frontend**: Next.js 15, React 18, Tailwind CSS
- **Backend**: Next.js API Routes, Server Actions
- **Database**: Supabase PostgreSQL
- **Auth**: Supabase Auth
- **Email**: Resend
- **Deployment**: Vercel
- **Monorepo**: Turbo

## 🔐 Security

- Row Level Security (RLS) enabled
- API rate limiting
- Bot detection algorithms
- Secure payment processing
- Data encryption

## 📈 Monitoring

- Real-time analytics
- Bot detection alerts
- Performance monitoring
- Error tracking
- User behavior analysis
