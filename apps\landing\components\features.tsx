"use client"

import { motion } from "framer-motion"
import { Shield, Zap, TrendingUp, Users, Eye, CreditCard } from "lucide-react"

const features = [
  {
    icon: Shield,
    title: "Anti-Bot Detection",
    description: "Sistem AI canggih yang mendeteksi bot views dengan akurasi 95%+",
    color: "blue",
  },
  {
    icon: Zap,
    title: "Real-time Tracking",
    description: "Monitor views, engagement, dan earnings secara real-time",
    color: "purple",
  },
  {
    icon: TrendingUp,
    title: "Analytics Mendalam",
    description: "Dashboard analytics lengkap untuk optimasi campaign",
    color: "green",
  },
  {
    icon: Users,
    title: "Community Promoter",
    description: "Akses ke ribuan promoter aktif dan terverifikasi",
    color: "orange",
  },
  {
    icon: Eye,
    title: "Transparent Pricing",
    description: "Sistem pricing yang jelas tanpa biaya tersembunyi",
    color: "red",
  },
  {
    icon: CreditCard,
    title: "Payout Otomatis",
    description: "Pembayaran otomatis setiap hari dengan minimum Rp 50.000",
    color: "indigo",
  },
]

export function Features() {
  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Fitur Unggulan Platform</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Dilengkapi dengan teknologi terdepan untuk memastikan campaign Anda berjalan optimal dan menghasilkan
            engagement yang berkualitas.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {features.map((feature, index) => (
            <motion.div
              key={feature.title}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-shadow border border-gray-100"
            >
              <div className={`w-12 h-12 bg-${feature.color}-100 rounded-lg flex items-center justify-center mb-6`}>
                <feature.icon className={`h-6 w-6 text-${feature.color}-600`} />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
              <p className="text-gray-600 leading-relaxed">{feature.description}</p>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
