
"use client"

import { useFormState, useFormStatus } from "react-dom"
// import { createSubmission } from "@/app/campaigns/[id]/actions"
import { Button } from "@creatorboost/ui/button"
import { Input } from "@creatorboost/ui/input"
import { Label } from "@creatorboost/ui/label"
import { useEffect } from "react"
import { useToast } from "@creatorboost/ui/use-toast"

const initialState = {
  error: null,
  success: null,
}

function SubmitButton() {
  const { pending } = useFormStatus()
  return (
    <Button className="w-full" type="submit" disabled={pending}>
      {pending ? "Submitting..." : "Submit for Tracking"}
    </Button>
  )
}

export function SubmissionForm({ campaignId }: { campaignId: string }) {
  // const [state, formAction] = useFormState(createSubmission, initialState)
  const { toast } = useToast()

  // useEffect(() => {
  //   if (state.error) {
  //     toast({
  //       variant: "destructive",
  //       title: "Uh oh! Something went wrong.",
  //       description: state.error,
  //     })
  //   }
  //   if (state.success) {
  //     toast({
  //       title: "Success!",
  //       description: state.success,
  //     })
  //   }
  // }, [state, toast])

  return (
    <form className="space-y-4">
      <input type="hidden" name="campaignId" value={campaignId} />
      <div className="space-y-2">
        <label htmlFor="postUrl">Post URL</label>
        <input id="postUrl" name="postUrl" placeholder="https://www.tiktok.com/..." required className="w-full px-3 py-2 border rounded" />
      </div>
      <button type="submit" className="w-full px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
        Submit Post
      </button>
    </form>
  )
}
