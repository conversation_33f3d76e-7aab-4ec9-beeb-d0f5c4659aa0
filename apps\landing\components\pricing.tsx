"use client"

import { motion } from "framer-motion"
import { Check, Star } from "lucide-react"
import { But<PERSON> } from "@creatorboost/ui"

const plans = [
  {
    name: "Basic",
    price: "Gratis",
    description: "Untuk content creator pemula",
    features: [
      "Komisi platform 10%",
      "Dashboard analytics dasar",
      "Support email",
      "Bot detection standar",
      "Payout mingguan",
    ],
    popular: false,
  },
  {
    name: "Pro",
    price: "Rp 99.000",
    period: "/bulan",
    description: "Untuk creator yang serius",
    features: [
      "Komisi platform 8%",
      "Analytics mendalam",
      "Priority support",
      "Advanced bot detection",
      "Payout harian",
      "Custom requirements",
      "Bulk campaign management",
    ],
    popular: true,
  },
  {
    name: "Enterprise",
    price: "Rp 299.000",
    period: "/bulan",
    description: "Untuk agency dan brand besar",
    features: [
      "Komisi platform 5%",
      "White-label dashboard",
      "Dedicated account manager",
      "AI-powered insights",
      "Instant payout",
      "API access",
      "Custom integrations",
      "Multi-user management",
    ],
    popular: false,
  },
]

export function Pricing() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl font-bold text-gray-900 mb-4">Pilih Paket yang Tepat</h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Mulai gratis dan upgrade sesuai kebutuhan bisnis Anda. Semua paket dilengkapi dengan fitur keamanan
            terdepan.
          </p>
        </motion.div>

        <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className={`relative bg-white rounded-2xl p-8 shadow-lg ${
                plan.popular ? "ring-2 ring-blue-500 scale-105" : ""
              }`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-semibold flex items-center">
                    <Star className="w-4 h-4 mr-1" />
                    Paling Populer
                  </div>
                </div>
              )}

              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                <p className="text-gray-600 mb-4">{plan.description}</p>
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-gray-900">{plan.price}</span>
                  {plan.period && <span className="text-gray-600 ml-1">{plan.period}</span>}
                </div>
              </div>

              <ul className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <Check className="w-5 h-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              <Button
                className={`w-full ${plan.popular ? "bg-blue-600 hover:bg-blue-700" : "bg-gray-900 hover:bg-gray-800"}`}
                size="lg"
              >
                {plan.name === "Basic" ? "Mulai Gratis" : "Pilih Paket"}
              </Button>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
