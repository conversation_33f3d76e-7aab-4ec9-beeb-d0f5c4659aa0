import { createSupabaseServerClient } from "@creatorboost/database"
import { redirect } from "next/navigation"
import { cookies } from "next/headers"
// import { Toaster } from "@creatorboost/ui/toaster"
// import { MainNav } from "@/components/main-nav"
// import { UserNav } from "@/components/user-nav"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export default async function DashboardLayout({ children }: DashboardLayoutProps) {
  const cookieStore = await cookies()
  const supabase = createSupabaseServerClient(cookieStore)

  const {
    data: { session },
  } = await supabase.auth.getSession()

  if (!session) {
    redirect("/login") // Assuming a login page exists at /login
  }

  return (
    <>
        <div className="hidden flex-col md:flex">
          <div className="border-b">
            <div className="flex h-16 items-center px-4">
              <nav className="mx-6">
                <h1 className="text-lg font-semibold">Dashboard</h1>
              </nav>
              <div className="ml-auto flex items-center space-x-4">
                <div className="text-sm">User Navigation</div>
              </div>
            </div>
          </div>
          <div className="flex-1 space-y-4 p-8 pt-6">{children}</div>
        </div>
        {/* <Toaster /> */}
    </>
  )
}