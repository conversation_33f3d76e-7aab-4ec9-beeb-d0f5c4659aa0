[04:37:57.745] Running build in Washington, D.C., USA (East) – iad1
[04:37:57.745] Build machine configuration: 2 cores, 8 GB
[04:37:57.764] Cloning github.com/rendoarsandi/creatorboost (Branch: main, Commit: d6667a2)
[04:37:57.965] Previous build caches not available
[04:37:58.163] Cloning completed: 398.000ms
[04:37:58.533] Running "vercel build"
[04:37:59.355] Vercel CLI 43.3.0
[04:37:59.506] > Detected Turbo. Adjusting default settings...
[04:37:59.914] Running "install" command: `npm install`...
[04:38:02.552] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[04:38:03.075] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[04:38:03.155] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[04:38:04.441] npm warn deprecated @supabase/auth-helpers-shared@0.6.3: This package is now deprecated - please use the @supabase/ssr package instead.
[04:38:04.585] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[04:38:04.612] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[04:38:04.926] npm warn deprecated @supabase/auth-helpers-nextjs@0.8.7: This package is now deprecated - please use the @supabase/ssr package instead.
[04:38:07.147] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[04:38:18.292] 
[04:38:18.294] added 485 packages, and audited 490 packages in 18s
[04:38:18.295] 
[04:38:18.296] 142 packages are looking for funding
[04:38:18.296]   run `npm fund` for details
[04:38:18.297] 
[04:38:18.299] found 0 vulnerabilities
[04:38:18.343] Detected Next.js version: 15.3.4
[04:38:18.343] Running "cd ../.. && npx turbo run build --filter=@creatorboost/dashboard"
[04:38:19.234] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[04:38:21.531] 
[04:38:21.532] Attention:
[04:38:21.532] Turborepo now collects completely anonymous telemetry regarding usage.
[04:38:21.532] This information is used to shape the Turborepo roadmap and prioritize features.
[04:38:21.532] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:38:21.532] https://turbo.build/repo/docs/telemetry
[04:38:21.533] 
[04:38:21.590] • Packages in scope: @creatorboost/dashboard
[04:38:21.590] • Running build in 1 packages
[04:38:21.591] • Remote caching enabled
[04:38:21.733] @creatorboost/dashboard:build: cache miss, executing d28f60edad6840a4
[04:38:21.911] @creatorboost/dashboard:build: 
[04:38:21.912] @creatorboost/dashboard:build: > @creatorboost/dashboard@0.1.0 build
[04:38:21.912] @creatorboost/dashboard:build: > next build
[04:38:21.912] @creatorboost/dashboard:build: 
[04:38:23.040] @creatorboost/dashboard:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[04:38:23.041] @creatorboost/dashboard:build: This information is used to shape Next.js' roadmap and prioritize features.
[04:38:23.041] @creatorboost/dashboard:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:38:23.042] @creatorboost/dashboard:build: https://nextjs.org/telemetry
[04:38:23.042] @creatorboost/dashboard:build: 
[04:38:23.186] @creatorboost/dashboard:build:    ▲ Next.js 15.3.4
[04:38:23.187] @creatorboost/dashboard:build: 
[04:38:23.437] @creatorboost/dashboard:build:    Creating an optimized production build ...
[04:38:30.582] @creatorboost/dashboard:build: Failed to compile.
[04:38:30.583] @creatorboost/dashboard:build: 
[04:38:30.583] @creatorboost/dashboard:build: ./app/chat/page.tsx
[04:38:30.583] @creatorboost/dashboard:build: Module not found: Can't resolve '@creatorboost/hooks'
[04:38:30.583] @creatorboost/dashboard:build: 
[04:38:30.583] @creatorboost/dashboard:build: https://nextjs.org/docs/messages/module-not-found
[04:38:30.583] @creatorboost/dashboard:build: 
[04:38:30.583] @creatorboost/dashboard:build: ./app/creator/page.tsx
[04:38:30.583] @creatorboost/dashboard:build: Module not found: Can't resolve '@creatorboost/hooks'
[04:38:30.583] @creatorboost/dashboard:build: 
[04:38:30.583] @creatorboost/dashboard:build: https://nextjs.org/docs/messages/module-not-found
[04:38:30.583] @creatorboost/dashboard:build: 
[04:38:30.584] @creatorboost/dashboard:build: ./components/notification-dropdown.tsx
[04:38:30.584] @creatorboost/dashboard:build: Module not found: Can't resolve '@creatorboost/hooks'
[04:38:30.584] @creatorboost/dashboard:build: 
[04:38:30.584] @creatorboost/dashboard:build: https://nextjs.org/docs/messages/module-not-found
[04:38:30.584] @creatorboost/dashboard:build: 
[04:38:30.599] @creatorboost/dashboard:build: 
[04:38:30.599] @creatorboost/dashboard:build: > Build failed because of webpack errors
[04:38:30.613] @creatorboost/dashboard:build: npm error Lifecycle script `build` failed with error:
[04:38:30.613] @creatorboost/dashboard:build: npm error code 1
[04:38:30.614] @creatorboost/dashboard:build: npm error path /vercel/path0/apps/dashboard
[04:38:30.614] @creatorboost/dashboard:build: npm error workspace @creatorboost/dashboard@0.1.0
[04:38:30.614] @creatorboost/dashboard:build: npm error location /vercel/path0/apps/dashboard
[04:38:30.614] @creatorboost/dashboard:build: npm error command failed
[04:38:30.614] @creatorboost/dashboard:build: npm error command sh -c next build
[04:38:30.621] @creatorboost/dashboard:build: ERROR: command finished with error: command (/vercel/path0/apps/dashboard) /node22/bin/npm run build exited (1)
[04:38:30.621] @creatorboost/dashboard#build: command (/vercel/path0/apps/dashboard) /node22/bin/npm run build exited (1)
[04:38:30.622] 
[04:38:30.622]   Tasks:    0 successful, 1 total
[04:38:30.622]  Cached:    0 cached, 1 total
[04:38:30.622]    Time:    9.082s 
[04:38:30.622] Summary:    /vercel/path0/.turbo/runs/2zARV3BKXT9mEBhRtdHD4VuuFi4.json
[04:38:30.623]  Failed:    @creatorboost/dashboard#build
[04:38:30.623] 
[04:38:30.627]  ERROR  run failed: command  exited (1)
[04:38:30.743] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/dashboard" exited with 1
[04:38:31.033] 
[04:38:33.747] Exiting build container