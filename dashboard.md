[06:02:09.471] Running build in Washington, D.C., USA (East) – iad1
[06:02:09.471] Build machine configuration: 2 cores, 8 GB
[06:02:09.513] Cloning github.com/rendoarsandi/creatorboost (Branch: feature/core-patch, Commit: ab1ae55)
[06:02:09.781] Previous build caches not available
[06:02:09.946] Cloning completed: 433.000ms
[06:02:10.287] Running "vercel build"
[06:02:10.709] Vercel CLI 43.3.0
[06:02:10.856] > Detected Turbo. Adjusting default settings...
[06:02:10.984] Running "install" command: `npm install`...
[06:02:13.426] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[06:02:13.943] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[06:02:14.059] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:02:15.372] npm warn deprecated @supabase/auth-helpers-shared@0.6.3: This package is now deprecated - please use the @supabase/ssr package instead.
[06:02:15.484] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[06:02:15.516] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[06:02:15.878] npm warn deprecated @supabase/auth-helpers-nextjs@0.8.7: This package is now deprecated - please use the @supabase/ssr package instead.
[06:02:17.967] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[06:02:29.239] 
[06:02:29.239] added 503 packages, and audited 509 packages in 18s
[06:02:29.239] 
[06:02:29.240] 142 packages are looking for funding
[06:02:29.240]   run `npm fund` for details
[06:02:29.243] 
[06:02:29.243] 2 low severity vulnerabilities
[06:02:29.243] 
[06:02:29.244] To address all issues (including breaking changes), run:
[06:02:29.244]   npm audit fix --force
[06:02:29.245] 
[06:02:29.245] Run `npm audit` for details.
[06:02:29.303] Detected Next.js version: 15.3.4
[06:02:29.304] Running "cd ../.. && npx turbo run build --filter=@creatorboost/dashboard"
[06:02:30.372] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[06:02:32.603] 
[06:02:32.603] Attention:
[06:02:32.603] Turborepo now collects completely anonymous telemetry regarding usage.
[06:02:32.603] This information is used to shape the Turborepo roadmap and prioritize features.
[06:02:32.603] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:02:32.603] https://turbo.build/repo/docs/telemetry
[06:02:32.604] 
[06:02:32.639] • Packages in scope: @creatorboost/dashboard
[06:02:32.639] • Running build in 1 packages
[06:02:32.639] • Remote caching enabled
[06:02:32.798] @creatorboost/dashboard:build: cache miss, executing c58e88cb246e6619
[06:02:32.926] @creatorboost/dashboard:build: 
[06:02:32.927] @creatorboost/dashboard:build: > @creatorboost/dashboard@0.1.0 build
[06:02:32.927] @creatorboost/dashboard:build: > next build
[06:02:32.930] @creatorboost/dashboard:build: 
[06:02:33.502] @creatorboost/dashboard:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[06:02:33.503] @creatorboost/dashboard:build: This information is used to shape Next.js' roadmap and prioritize features.
[06:02:33.503] @creatorboost/dashboard:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:02:33.504] @creatorboost/dashboard:build: https://nextjs.org/telemetry
[06:02:33.504] @creatorboost/dashboard:build: 
[06:02:33.559] @creatorboost/dashboard:build:    ▲ Next.js 15.3.4
[06:02:33.560] @creatorboost/dashboard:build: 
[06:02:33.624] @creatorboost/dashboard:build:    Creating an optimized production build ...
[06:02:43.881] @creatorboost/dashboard:build: <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (100kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:02:49.783] @creatorboost/dashboard:build:  ✓ Compiled successfully in 11.0s
[06:02:49.786] @creatorboost/dashboard:build:    Linting and checking validity of types ...
[06:02:55.677] @creatorboost/dashboard:build: Failed to compile.
[06:02:55.678] @creatorboost/dashboard:build: 
[06:02:55.678] @creatorboost/dashboard:build: ../../packages/database/lib/supabase.ts:2:25
[06:02:55.678] @creatorboost/dashboard:build: Type error: Cannot find module 'next/headers' or its corresponding type declarations.
[06:02:55.679] @creatorboost/dashboard:build: 
[06:02:55.679] @creatorboost/dashboard:build: [0m [90m 1 |[39m [36mimport[39m { createBrowserClient[33m,[39m createServerClient[33m,[39m type [33mCookieOptions[39m } [36mfrom[39m [32m'@supabase/ssr'[39m[0m
[06:02:55.679] @creatorboost/dashboard:build: [0m[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { cookies } [36mfrom[39m [32m'next/headers'[39m[0m
[06:02:55.680] @creatorboost/dashboard:build: [0m [90m   |[39m                         [31m[1m^[22m[39m[0m
[06:02:55.680] @creatorboost/dashboard:build: [0m [90m 3 |[39m[0m
[06:02:55.680] @creatorboost/dashboard:build: [0m [90m 4 |[39m [90m// Define a function to create a Supabase client for server-side operations[39m[0m
[06:02:55.680] @creatorboost/dashboard:build: [0m [90m 5 |[39m [36mexport[39m [36mfunction[39m createSupabaseServerClient(cookieStore[33m:[39m [33mReturnType[39m[33m<[39m[36mtypeof[39m cookies[33m>[39m) {[0m
[06:02:55.698] @creatorboost/dashboard:build: Next.js build worker exited with code: 1 and signal: null
[06:02:55.704] @creatorboost/dashboard:build: npm error Lifecycle script `build` failed with error:
[06:02:55.705] @creatorboost/dashboard:build: npm error code 1
[06:02:55.705] @creatorboost/dashboard:build: npm error path /vercel/path0/apps/dashboard
[06:02:55.706] @creatorboost/dashboard:build: npm error workspace @creatorboost/dashboard@0.1.0
[06:02:55.706] @creatorboost/dashboard:build: npm error location /vercel/path0/apps/dashboard
[06:02:55.706] @creatorboost/dashboard:build: npm error command failed
[06:02:55.706] @creatorboost/dashboard:build: npm error command sh -c next build
[06:02:55.711] @creatorboost/dashboard:build: ERROR: command finished with error: command (/vercel/path0/apps/dashboard) /node22/bin/npm run build exited (1)
[06:02:55.713] @creatorboost/dashboard#build: command (/vercel/path0/apps/dashboard) /node22/bin/npm run build exited (1)
[06:02:55.714] 
[06:02:55.714]   Tasks:    0 successful, 1 total
[06:02:55.714]  Cached:    0 cached, 1 total
[06:02:55.714]    Time:    23.105s 
[06:02:55.715] Summary:    /vercel/path0/.turbo/runs/2zAblVWvYuMw8w1BmSndP7c8Idu.json
[06:02:55.715]  Failed:    @creatorboost/dashboard#build
[06:02:55.715] 
[06:02:55.718]  ERROR  run failed: command  exited (1)
[06:02:55.795] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/dashboard" exited with 1
[06:02:56.100] 
[06:02:58.998] Exiting build container