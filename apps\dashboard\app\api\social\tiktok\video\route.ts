import { NextRequest, NextResponse } from 'next/server'

interface TikTokVideoResponse {
  data: {
    videos: Array<{
      id: string
      view_count: number
      like_count: number
      comment_count: number
      share_count: number
      create_time: number
      video_description: string
      username: string
      display_name: string
    }>
  }
  error?: {
    code: string
    message: string
  }
}

export async function POST(request: NextRequest) {
  try {
    const { videoId } = await request.json()
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const accessToken = authHeader.substring(7)

    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    // TikTok Research API endpoint
    const tiktokApiUrl = 'https://open.tiktokapis.com/v2/research/video/query/'
    
    const requestBody = {
      query: {
        and: [
          {
            operation: "EQ",
            field_name: "video_id",
            field_values: [videoId]
          }
        ]
      },
      fields: [
        "id",
        "view_count",
        "like_count", 
        "comment_count",
        "share_count",
        "create_time",
        "video_description",
        "username",
        "display_name"
      ],
      max_count: 1
    }

    const response = await fetch(tiktokApiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestBody)
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('TikTok API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch video data from TikTok' },
        { status: response.status }
      )
    }

    const data: TikTokVideoResponse = await response.json()

    if (data.error) {
      return NextResponse.json(
        { error: data.error.message },
        { status: 400 }
      )
    }

    if (!data.data.videos || data.data.videos.length === 0) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    const video = data.data.videos[0]

    // Return standardized video data
    return NextResponse.json({
      id: video.id,
      view_count: video.view_count,
      like_count: video.like_count,
      comment_count: video.comment_count,
      share_count: video.share_count,
      create_time: video.create_time,
      video_description: video.video_description,
      username: video.username,
      display_name: video.display_name
    })

  } catch (error) {
    console.error('TikTok API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Alternative endpoint for TikTok Display API (for user's own content)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const videoId = searchParams.get('video_id')
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const accessToken = authHeader.substring(7)

    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      )
    }

    // TikTok Display API endpoint for user's videos
    const tiktokApiUrl = `https://open.tiktokapis.com/v2/video/list/?fields=id,view_count,like_count,comment_count,share_count,create_time,title,username,display_name`
    
    const response = await fetch(tiktokApiUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        max_count: 20,
        cursor: 0
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('TikTok Display API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch video data from TikTok' },
        { status: response.status }
      )
    }

    const data = await response.json()

    // Find the specific video by ID
    const video = data.data.videos?.find((v: any) => v.id === videoId)

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found in user\'s videos' },
        { status: 404 }
      )
    }

    return NextResponse.json(video)

  } catch (error) {
    console.error('TikTok Display API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
