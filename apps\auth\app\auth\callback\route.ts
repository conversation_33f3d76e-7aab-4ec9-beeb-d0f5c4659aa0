
import { createServerClient, type CookieOptions } from '@supabase/ssr'
import { cookies } from 'next/headers'
import { NextResponse } from 'next/server'

import type { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get('code')

  if (code) {
    const cookieStore = await cookies()
    const supabase = createServerClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name: string) {
            return (cookieStore as any).get(name)?.value
          },
          set(name: string, value: string, options: CookieOptions) {
            (cookieStore as any).set({ name, value, ...options })
          },
          remove(name: string, options: CookieOptions) {
            (cookieStore as any).delete({ name, ...options })
          },
        },
      }
    )

    const { data, error } = await supabase.auth.exchangeCodeForSession(code)

    if (data.session && data.user) {
      // Check if user profile exists, if not create one
      const { data: profile, error: profileError } = await supabase
        .from('users')
        .select('role')
        .eq('id', data.user.id)
        .single()

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create one with default role
        const { error: insertError } = await supabase
          .from('users')
          .insert({
            id: data.user.id,
            email: data.user.email,
            full_name: data.user.user_metadata?.full_name || data.user.email?.split('@')[0],
            role: 'creator' // Default role
          })

        if (!insertError) {
          // Redirect to dashboard for new users
          return NextResponse.redirect(process.env.NEXT_PUBLIC_DASHBOARD_URL || `${requestUrl.origin}/dashboard`)
        }
      } else if (profile) {
        // Handle social media OAuth providers
        const provider = data.session.user.app_metadata?.provider
        if (provider && ['tiktok', 'instagram', 'youtube'].includes(provider)) {
          // Store social media account information
          const socialAccountData = {
            user_id: data.user.id,
            platform: provider,
            platform_user_id: data.user.user_metadata?.provider_id || data.user.id,
            username: data.user.user_metadata?.user_name || data.user.user_metadata?.preferred_username || data.user.email?.split('@')[0],
            display_name: data.user.user_metadata?.full_name || data.user.user_metadata?.name,
            profile_picture_url: data.user.user_metadata?.avatar_url || data.user.user_metadata?.picture,
            follower_count: data.user.user_metadata?.public_metrics?.followers_count || 0,
            access_token: data.session.provider_token || '',
            refresh_token: data.session.provider_refresh_token,
          }

          // Insert or update social media account
          await supabase
            .from('social_media_accounts')
            .upsert(socialAccountData, {
              onConflict: 'user_id,platform,platform_user_id'
            })
        }

        // Redirect based on user role
        if (profile.role === 'admin') {
          return NextResponse.redirect(process.env.NEXT_PUBLIC_ADMIN_URL || `${requestUrl.origin}/admin`)
        } else {
          return NextResponse.redirect(process.env.NEXT_PUBLIC_DASHBOARD_URL || `${requestUrl.origin}/dashboard`)
        }
      }
    }
  }

  // Default redirect to landing page
  return NextResponse.redirect(process.env.NEXT_PUBLIC_LANDING_URL || requestUrl.origin)
}
