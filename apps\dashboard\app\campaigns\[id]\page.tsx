
import { createSupabaseServerClient } from "@creatorboost/database";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";
// import { Card, CardContent, CardDescription, CardHeader, CardTitle, Button } from "@creatorboost/ui";
// import { SubmissionForm } from "@/components/submission-form"; // Updated import

type CampaignDetailPageProps = {
  params: Promise<{
    id: string;
  }>;
};

async function getCampaign(id: string) {
  const cookieStore = await cookies();
  const supabase = createSupabaseServerClient(cookieStore);
  const { data: campaign, error } = await supabase
    .from("campaigns")
    .select(`
      *,
      profiles (
        full_name,
        avatar_url
      )
    `)
    .eq("id", id)
    .single();

  if (error || !campaign) {
    notFound();
  }

  return campaign;
}

export default async function CampaignDetailPage({ params }: CampaignDetailPageProps) {
  const { id } = await params;
  const campaign = await getCampaign(id);

  return (
    <>
      <div className="flex items-center justify-between space-y-2 mb-4">
        <h2 className="text-3xl font-bold tracking-tight">{campaign.name}</h2>
      </div>
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <div className="border rounded-lg p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">Campaign Details</h3>
              <p className="text-gray-600">{campaign.description}</p>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="font-semibold">Requirements</h3>
                <p className="text-sm text-gray-500">
                  {campaign.requirements || "No specific requirements provided."}
                </p>
              </div>
              <div>
                <h3 className="font-semibold">Materials</h3>
                <a
                  href={campaign.materials_url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-block px-4 py-2 border rounded text-sm hover:bg-gray-50"
                >
                  Download Materials
                </a>
              </div>
            </div>
          </div>
        </div>
        <div className="lg:col-span-1">
          <div className="border rounded-lg p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold">Submit Your Post</h3>
              <p className="text-gray-600">
                Paste the URL of your TikTok or Instagram post below.
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500">Submission form will be implemented here.</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
