export * from "./lib/bot-detection-engine"
export * from "./lib/analyzer"
export * from "./types"

//----------------------------------------------
// Helper so you never “call” a class by mistake
//----------------------------------------------
import { BotDetectionEngine } from "./lib/bot-detection-engine"
import type { ViewData, BotDetectionResult } from "./types"

/**
 * Functional helper – use this instead of invoking the class:
 *
 *   import { calculateBotScore } from "@creatorboost/bot-detection"
 *   const result = calculateBotScore(viewData)
 */
export function calculateBotScore(viewData: ViewData): BotDetectionResult {
  return BotDetectionEngine.calculateBotScore(viewData)
}

/**
 * Default export so you can also do:
 *   import calculateBotScore from "@creatorboost/bot-detection"
 */
export default calculateBotScore
