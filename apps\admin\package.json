{"name": "@creatorboost/admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3003", "build": "next build", "start": "next start -p 3003", "lint": "next lint"}, "dependencies": {"@creatorboost/bot-detection": "*", "@creatorboost/database": "*", "@creatorboost/ui": "*", "@supabase/auth-helpers-nextjs": "^0.8.7", "@supabase/supabase-js": "^2.39.0", "date-fns": "^2.30.0", "next": "^15.3.4", "recharts": "^2.8.0"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-next": "^15.3.4", "typescript": "^5.3.2"}}