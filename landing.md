[06:01:44.482] Running build in Washington, D.C., USA (East) – iad1
[06:01:44.482] Build machine configuration: 2 cores, 8 GB
[06:01:44.512] Cloning github.com/rendoarsandi/creatorboost (Branch: feature/core-patch, Commit: ab1ae55)
[06:01:44.775] Previous build caches not available
[06:01:45.012] Cloning completed: 500.000ms
[06:01:45.317] Running "vercel build"
[06:01:45.749] Vercel CLI 43.3.0
[06:01:45.931] > Detected Turbo. Adjusting default settings...
[06:01:46.060] Running "install" command: `npm install`...
[06:01:48.572] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[06:01:49.000] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[06:01:49.239] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:01:50.390] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[06:01:50.422] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[06:01:52.536] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[06:01:59.690] 
[06:01:59.691] added 444 packages, and audited 447 packages in 13s
[06:01:59.692] 
[06:01:59.692] 137 packages are looking for funding
[06:01:59.692]   run `npm fund` for details
[06:01:59.692] 
[06:01:59.693] found 0 vulnerabilities
[06:01:59.751] Detected Next.js version: 15.3.4
[06:01:59.752] Running "cd ../.. && npx turbo run build --filter=@creatorboost/landing"
[06:02:00.636] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[06:02:02.897] 
[06:02:02.898] Attention:
[06:02:02.898] Turborepo now collects completely anonymous telemetry regarding usage.
[06:02:02.898] This information is used to shape the Turborepo roadmap and prioritize features.
[06:02:02.898] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:02:02.898] https://turbo.build/repo/docs/telemetry
[06:02:02.899] 
[06:02:02.934] • Packages in scope: @creatorboost/landing
[06:02:02.935] • Running build in 1 packages
[06:02:02.935] • Remote caching enabled
[06:02:03.067] @creatorboost/landing:build: cache miss, executing 47785f1620645793
[06:02:03.208] @creatorboost/landing:build: 
[06:02:03.209] @creatorboost/landing:build: > @creatorboost/landing@0.1.0 build
[06:02:03.209] @creatorboost/landing:build: > next build
[06:02:03.209] @creatorboost/landing:build: 
[06:02:03.815] @creatorboost/landing:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[06:02:03.816] @creatorboost/landing:build: This information is used to shape Next.js' roadmap and prioritize features.
[06:02:03.816] @creatorboost/landing:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:02:03.816] @creatorboost/landing:build: https://nextjs.org/telemetry
[06:02:03.816] @creatorboost/landing:build: 
[06:02:03.875] @creatorboost/landing:build:    ▲ Next.js 15.3.4
[06:02:03.875] @creatorboost/landing:build: 
[06:02:03.937] @creatorboost/landing:build:    Creating an optimized production build ...
[06:02:07.914] @creatorboost/landing:build: Failed to compile.
[06:02:07.916] @creatorboost/landing:build: 
[06:02:07.916] @creatorboost/landing:build: app/layout.tsx
[06:02:07.916] @creatorboost/landing:build: An error occurred in `next/font`.
[06:02:07.916] @creatorboost/landing:build: 
[06:02:07.917] @creatorboost/landing:build: Error: Cannot find module '@tailwindcss/postcss'
[06:02:07.917] @creatorboost/landing:build: Require stack:
[06:02:07.917] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js
[06:02:07.917] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/index.js
[06:02:07.917] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/index.js
[06:02:07.918] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack-config.js
[06:02:07.918] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack-build/impl.js
[06:02:07.918] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/compiled/jest-worker/processChild.js
[06:02:07.919] @creatorboost/landing:build:     at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
[06:02:07.919] @creatorboost/landing:build:     at /vercel/path0/apps/landing/node_modules/next/dist/server/require-hook.js:55:36
[06:02:07.919] @creatorboost/landing:build:     at Function.resolve (node:internal/modules/helpers:145:19)
[06:02:07.919] @creatorboost/landing:build:     at loadPlugin (/vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:53:32)
[06:02:07.920] @creatorboost/landing:build:     at /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:185:56
[06:02:07.920] @creatorboost/landing:build:     at Array.map (<anonymous>)
[06:02:07.920] @creatorboost/landing:build:     at getPostCssPlugins (/vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:185:47)
[06:02:07.920] @creatorboost/landing:build:     at async /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/index.js:125:36
[06:02:07.921] @creatorboost/landing:build:     at async /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js:94:33
[06:02:07.921] @creatorboost/landing:build:     at async Span.traceAsyncFn (/vercel/path0/apps/landing/node_modules/next/dist/trace/trace.js:157:20)
[06:02:07.921] @creatorboost/landing:build: 
[06:02:07.929] @creatorboost/landing:build: 
[06:02:07.930] @creatorboost/landing:build: > Build failed because of webpack errors
[06:02:07.943] @creatorboost/landing:build: npm error Lifecycle script `build` failed with error:
[06:02:07.946] @creatorboost/landing:build: npm error code 1
[06:02:07.947] @creatorboost/landing:build: npm error path /vercel/path0/apps/landing
[06:02:07.947] @creatorboost/landing:build: npm error workspace @creatorboost/landing@0.1.0
[06:02:07.948] @creatorboost/landing:build: npm error location /vercel/path0/apps/landing
[06:02:07.948] @creatorboost/landing:build: npm error command failed
[06:02:07.948] @creatorboost/landing:build: npm error command sh -c next build
[06:02:07.952] @creatorboost/landing:build: ERROR: command finished with error: command (/vercel/path0/apps/landing) /node22/bin/npm run build exited (1)
[06:02:07.952] @creatorboost/landing#build: command (/vercel/path0/apps/landing) /node22/bin/npm run build exited (1)
[06:02:07.953] 
[06:02:07.953]   Tasks:    0 successful, 1 total
[06:02:07.953]  Cached:    0 cached, 1 total
[06:02:07.953]    Time:    5.051s 
[06:02:07.953] Summary:    /vercel/path0/.turbo/runs/2zAbfTb4rVigl7HsSDRF7iCjsF3.json
[06:02:07.953]  Failed:    @creatorboost/landing#build
[06:02:07.953] 
[06:02:07.957]  ERROR  run failed: command  exited (1)
[06:02:08.034] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/landing" exited with 1
[06:02:08.279] 
[06:02:11.263] Exiting build container