[04:38:31.952] Running build in Washington, D.C., USA (East) – iad1
[04:38:31.953] Build machine configuration: 2 cores, 8 GB
[04:38:31.977] Cloning github.com/rendoarsandi/creatorboost (Branch: main, Commit: d6667a2)
[04:38:32.126] Previous build caches not available
[04:38:32.370] Cloning completed: 393.000ms
[04:38:32.795] Running "vercel build"
[04:38:33.553] Vercel CLI 43.3.0
[04:38:33.717] > Detected Turbo. Adjusting default settings...
[04:38:33.850] Running "install" command: `npm install`...
[04:38:37.304] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[04:38:37.648] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[04:38:37.738] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[04:38:38.961] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[04:38:38.988] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[04:38:41.205] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[04:38:48.272] 
[04:38:48.273] added 428 packages, and audited 431 packages in 14s
[04:38:48.273] 
[04:38:48.273] 137 packages are looking for funding
[04:38:48.274]   run `npm fund` for details
[04:38:48.274] 
[04:38:48.274] found 0 vulnerabilities
[04:38:48.317] Detected Next.js version: 15.3.4
[04:38:48.318] Running "cd ../.. && npx turbo run build --filter=@creatorboost/landing"
[04:38:49.216] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[04:38:52.506] 
[04:38:52.506] Attention:
[04:38:52.506] Turborepo now collects completely anonymous telemetry regarding usage.
[04:38:52.507] This information is used to shape the Turborepo roadmap and prioritize features.
[04:38:52.507] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:38:52.507] https://turbo.build/repo/docs/telemetry
[04:38:52.507] 
[04:38:52.566] • Packages in scope: @creatorboost/landing
[04:38:52.566] • Running build in 1 packages
[04:38:52.567] • Remote caching enabled
[04:38:52.683] @creatorboost/landing:build: cache miss, executing f7e398c1eb700986
[04:38:52.941] @creatorboost/landing:build: 
[04:38:52.943] @creatorboost/landing:build: > @creatorboost/landing@0.1.0 build
[04:38:52.943] @creatorboost/landing:build: > next build
[04:38:52.944] @creatorboost/landing:build: 
[04:38:54.284] @creatorboost/landing:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[04:38:54.285] @creatorboost/landing:build: This information is used to shape Next.js' roadmap and prioritize features.
[04:38:54.285] @creatorboost/landing:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:38:54.285] @creatorboost/landing:build: https://nextjs.org/telemetry
[04:38:54.286] @creatorboost/landing:build: 
[04:38:54.446] @creatorboost/landing:build:    ▲ Next.js 15.3.4
[04:38:54.446] @creatorboost/landing:build: 
[04:38:54.712] @creatorboost/landing:build:    Creating an optimized production build ...
[04:38:58.933] @creatorboost/landing:build: Failed to compile.
[04:38:58.934] @creatorboost/landing:build: 
[04:38:58.935] @creatorboost/landing:build: app/layout.tsx
[04:38:58.935] @creatorboost/landing:build: An error occurred in `next/font`.
[04:38:58.935] @creatorboost/landing:build: 
[04:38:58.935] @creatorboost/landing:build: Error: Cannot find module '@tailwindcss/postcss'
[04:38:58.936] @creatorboost/landing:build: Require stack:
[04:38:58.936] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js
[04:38:58.936] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/index.js
[04:38:58.936] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/index.js
[04:38:58.937] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack-config.js
[04:38:58.937] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/build/webpack-build/impl.js
[04:38:58.938] @creatorboost/landing:build: - /vercel/path0/apps/landing/node_modules/next/dist/compiled/jest-worker/processChild.js
[04:38:58.938] @creatorboost/landing:build:     at Function.<anonymous> (node:internal/modules/cjs/loader:1401:15)
[04:38:58.938] @creatorboost/landing:build:     at /vercel/path0/apps/landing/node_modules/next/dist/server/require-hook.js:55:36
[04:38:58.939] @creatorboost/landing:build:     at Function.resolve (node:internal/modules/helpers:145:19)
[04:38:58.939] @creatorboost/landing:build:     at loadPlugin (/vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:53:32)
[04:38:58.939] @creatorboost/landing:build:     at /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:185:56
[04:38:58.939] @creatorboost/landing:build:     at Array.map (<anonymous>)
[04:38:58.940] @creatorboost/landing:build:     at getPostCssPlugins (/vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/plugins.js:185:47)
[04:38:58.940] @creatorboost/landing:build:     at async /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/config/blocks/css/index.js:125:36
[04:38:58.940] @creatorboost/landing:build:     at async /vercel/path0/apps/landing/node_modules/next/dist/build/webpack/loaders/next-font-loader/index.js:94:33
[04:38:58.941] @creatorboost/landing:build:     at async Span.traceAsyncFn (/vercel/path0/apps/landing/node_modules/next/dist/trace/trace.js:157:20)
[04:38:58.941] @creatorboost/landing:build: 
[04:38:58.951] @creatorboost/landing:build: 
[04:38:58.952] @creatorboost/landing:build: > Build failed because of webpack errors
[04:38:58.965] @creatorboost/landing:build: npm error Lifecycle script `build` failed with error:
[04:38:58.969] @creatorboost/landing:build: npm error code 1
[04:38:58.969] @creatorboost/landing:build: npm error path /vercel/path0/apps/landing
[04:38:58.970] @creatorboost/landing:build: npm error workspace @creatorboost/landing@0.1.0
[04:38:58.970] @creatorboost/landing:build: npm error location /vercel/path0/apps/landing
[04:38:58.970] @creatorboost/landing:build: npm error command failed
[04:38:58.970] @creatorboost/landing:build: npm error command sh -c next build
[04:38:58.975] @creatorboost/landing:build: ERROR: command finished with error: command (/vercel/path0/apps/landing) /node22/bin/npm run build exited (1)
[04:38:58.975] @creatorboost/landing#build: command (/vercel/path0/apps/landing) /node22/bin/npm run build exited (1)
[04:38:58.977] 
[04:38:58.977]   Tasks:    0 successful, 1 total
[04:38:58.977]  Cached:    0 cached, 1 total
[04:38:58.977]    Time:    6.462s 
[04:38:58.977] Summary:    /vercel/path0/.turbo/runs/2zARYTLgyQdWQeAsLdPzSsK2syC.json
[04:38:58.977]  Failed:    @creatorboost/landing#build
[04:38:58.977] 
[04:38:58.981]  ERROR  run failed: command  exited (1)
[04:38:59.059] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/landing" exited with 1
[04:38:59.682] 
[04:39:02.540] Exiting build container