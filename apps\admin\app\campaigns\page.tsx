// apps/admin/app/campaigns/page.tsx
import { Input } from "@creatorboost/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@creatorboost/ui/table";
import { Badge } from "@creatorboost/ui/badge";
import { <PERSON><PERSON> } from "@creatorboost/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@creatorboost/ui/card";

export default function AdminCampaignsPage() {
  // Mock data for demonstration
  const campaigns = [
    {
      id: 1,
      name: "Summer Product Launch",
      creator: "John Doe",
      budget: 5000,
      status: "active",
      submissions: 12,
      views: 45000
    },
    {
      id: 2,
      name: "Brand Awareness Campaign",
      creator: "<PERSON>",
      budget: 3000,
      status: "pending",
      submissions: 8,
      views: 23000
    }
  ];

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Campaign Management</h1>
        <Button>Create Campaign</Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Search & Filter</CardTitle>
        </CardHeader>
        <CardContent>
          <Input placeholder="Search for campaigns..." className="max-w-sm" />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>All Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign Name</TableHead>
                <TableHead>Creator</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submissions</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {campaigns.length > 0 ? campaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell className="font-medium">{campaign.name}</TableCell>
                  <TableCell>{campaign.creator}</TableCell>
                  <TableCell>${campaign.budget.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge variant={campaign.status === 'active' ? 'default' : 'secondary'}>
                      {campaign.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{campaign.submissions}</TableCell>
                  <TableCell>{campaign.views.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">View</Button>
                      <Button variant="outline" size="sm">Edit</Button>
                    </div>
                  </TableCell>
                </TableRow>
              )) : (
                <TableRow>
                  <TableCell colSpan={7} className="text-center">No campaigns found.</TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
}
