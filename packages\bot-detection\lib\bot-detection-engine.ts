;/import type { ViewData, BotDetectionResult } from "../epsty
"

/* ---------- Public helpers ---------- */

/** Main helper – call this everywhere instead of instantiating a class. */
export function calculateBotScore(viewData: ViewData): BotDetectionResult {
  const velocityScore = calcVelocity(viewData.viewsPerMinute)
  const engagementScore = calcEngagement(viewData.engagementRate)
  const accountScore = calcAccountAge(viewData.accountAge)
  const patternScore = calcPattern(viewData.consistencyScore)

  const score = Math.round(velocityScore * 0.4 + engagementScore * 0.3 + accountScore * 0.2 + patternScore * 0.1)

  return {
    score,
    level: getLevel(score),
    details: { velocityScore, engagementScore, accountScore, patternScore },
  }
}

/** If you need the old API object-style access, it’s still here. */
export const BotDetectionEngine = {
  calculateBotScore,
  getActionForLevel(level: BotDetectionResult["level"]) {
    switch (level) {
      case "safe":
        return { action: "approve", payoutDelay: 0, description: "Normal" }
      case "warning":
        return {
          action: "delay",
          payoutDelay: 48,
          description: "Slightly suspicious – delay payout",
        }
      case "suspicious":
        return {
          action: "review",
          payoutDelay: 168,
          description: "Manual review required",
        }
      case "bot":
        return {
          action: "ban",
          payoutDelay: -1,
          description: "Bot detected – ban & forfeit",
        }
      default:
        return {
          action: "review",
          payoutDelay: 168,
          description: "Unknown level – review",
        }
    }
  },
}

/* ---------- Internal utilities ---------- */

const THRESHOLDS = { SAFE: 30, WARNING: 60, SUSPICIOUS: 80, BOT: 100 }

function getLevel(score: number): BotDetectionResult["level"] {
  if (score >= THRESHOLDS.BOT) return "bot"
  if (score >= THRESHOLDS.SUSPICIOUS) return "suspicious"
  if (score >= THRESHOLDS.WARNING) return "warning"
  return "safe"
}

function calcVelocity(vpm: number) {
  const ratio = vpm / 2.5
  if (ratio > 10) return 100
  if (ratio > 5) return 80
  if (ratio > 3) return 60
  if (ratio > 2) return 30
  return 0
}

function calcEngagement(rate: number) {
  if (rate < 0.005) return 90
  if (rate < 0.01) return 70
  if (rate < 0.02) return 40
  if (rate > 0.15) return 60
  return 0
}

function calcAccountAge(days: number) {
  if (days < 7) return 80
  if (days < 30) return 60
  if (days < 90) return 30
  return 0
}

function calcPattern(consistency: number) {
  if (consistency > 0.95) return 80
  if (consistency < 0.1) return 60
  return 0
}
