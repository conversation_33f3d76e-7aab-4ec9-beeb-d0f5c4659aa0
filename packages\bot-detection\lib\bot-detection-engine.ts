import type { ViewData, BotDetectionResult, SocialMediaMetrics, FraudDetectionResult } from "../types"

/* ---------- Public helpers ---------- */

/** Main helper – call this everywhere instead of instantiating a class. */
export function calculateBotScore(viewData: ViewData): BotDetectionResult {
  const velocityScore = calcVelocity(viewData.viewsPerMinute)
  const engagementScore = calcEngagement(viewData.engagementRate)
  const accountScore = calcAccountAge(viewData.accountAge)
  const patternScore = calcPattern(viewData.consistencyScore)

  const score = Math.round(velocityScore * 0.4 + engagementScore * 0.3 + accountScore * 0.2 + patternScore * 0.1)

  return {
    score,
    level: getLevel(score),
    details: { velocityScore, engagementScore, accountScore, patternScore },
  }
}

/** If you need the old API object-style access, it’s still here. */
export const BotDetectionEngine = {
  calculateBotScore,
  getActionForLevel(level: BotDetectionResult["level"]) {
    switch (level) {
      case "safe":
        return { action: "approve", payoutDelay: 0, description: "Normal" }
      case "warning":
        return {
          action: "delay",
          payoutDelay: 48,
          description: "Slightly suspicious – delay payout",
        }
      case "suspicious":
        return {
          action: "review",
          payoutDelay: 168,
          description: "Manual review required",
        }
      case "bot":
        return {
          action: "ban",
          payoutDelay: -1,
          description: "Bot detected – ban & forfeit",
        }
      default:
        return {
          action: "review",
          payoutDelay: 168,
          description: "Unknown level – review",
        }
    }
  },
}

/* ---------- Internal utilities ---------- */

const THRESHOLDS = { SAFE: 30, WARNING: 60, SUSPICIOUS: 80, BOT: 100 }

function getLevel(score: number): BotDetectionResult["level"] {
  if (score >= THRESHOLDS.BOT) return "bot"
  if (score >= THRESHOLDS.SUSPICIOUS) return "suspicious"
  if (score >= THRESHOLDS.WARNING) return "warning"
  return "safe"
}

function calcVelocity(vpm: number) {
  const ratio = vpm / 2.5
  if (ratio > 10) return 100
  if (ratio > 5) return 80
  if (ratio > 3) return 60
  if (ratio > 2) return 30
  return 0
}

function calcEngagement(rate: number) {
  if (rate < 0.005) return 90
  if (rate < 0.01) return 70
  if (rate < 0.02) return 40
  if (rate > 0.15) return 60
  return 0
}

/** Enhanced fraud detection for social media metrics */
export function detectSocialMediaFraud(metrics: SocialMediaMetrics): FraudDetectionResult {
  const flags: string[] = []
  let fraudScore = 0
  let payoutEligible = true
  let payoutMultiplier = 1.0

  // Platform-specific thresholds
  const thresholds = getPlatformThresholds(metrics.platform)

  // 1. Engagement Rate Analysis
  const engagementAnalysis = analyzeEngagementRate(metrics, thresholds)
  fraudScore += engagementAnalysis.score
  flags.push(...engagementAnalysis.flags)

  // 2. View-to-Engagement Ratio Analysis
  const ratioAnalysis = analyzeViewEngagementRatio(metrics, thresholds)
  fraudScore += ratioAnalysis.score
  flags.push(...ratioAnalysis.flags)

  // 3. Account Credibility Analysis
  const accountAnalysis = analyzeAccountCredibility(metrics.accountInfo)
  fraudScore += accountAnalysis.score
  flags.push(...accountAnalysis.flags)

  // 4. Temporal Pattern Analysis
  const temporalAnalysis = analyzeTemporalPatterns(metrics)
  fraudScore += temporalAnalysis.score
  flags.push(...temporalAnalysis.flags)

  // 5. Cross-Platform Consistency Check
  const consistencyAnalysis = analyzeCrossPlatformConsistency(metrics)
  fraudScore += consistencyAnalysis.score
  flags.push(...consistencyAnalysis.flags)

  // Determine final fraud level and payout decision
  const fraudLevel = getFraudLevel(fraudScore)
  const payoutDecision = getPayoutDecision(fraudLevel, fraudScore)

  return {
    fraudScore: Math.min(fraudScore, 100),
    fraudLevel,
    flags: flags.filter(Boolean),
    payoutEligible: payoutDecision.eligible,
    payoutMultiplier: payoutDecision.multiplier,
    payoutDelay: payoutDecision.delay,
    recommendation: payoutDecision.recommendation,
    details: {
      engagement: engagementAnalysis,
      ratios: ratioAnalysis,
      account: accountAnalysis,
      temporal: temporalAnalysis,
      consistency: consistencyAnalysis
    }
  }
}

function getPlatformThresholds(platform: string) {
  const baseThresholds = {
    tiktok: {
      minEngagementRate: 0.02,
      maxEngagementRate: 0.12,
      minViewLikeRatio: 10,
      maxViewLikeRatio: 100,
      minViewCommentRatio: 50,
      maxViewCommentRatio: 500,
      suspiciousGrowthRate: 5.0
    },
    instagram: {
      minEngagementRate: 0.01,
      maxEngagementRate: 0.08,
      minViewLikeRatio: 15,
      maxViewLikeRatio: 200,
      minViewCommentRatio: 100,
      maxViewCommentRatio: 1000,
      suspiciousGrowthRate: 3.0
    },
    youtube: {
      minEngagementRate: 0.005,
      maxEngagementRate: 0.05,
      minViewLikeRatio: 20,
      maxViewLikeRatio: 500,
      minViewCommentRatio: 200,
      maxViewCommentRatio: 2000,
      suspiciousGrowthRate: 2.0
    }
  }

  return baseThresholds[platform as keyof typeof baseThresholds] || baseThresholds.tiktok
}

function analyzeEngagementRate(metrics: SocialMediaMetrics, thresholds: any) {
  const flags: string[] = []
  let score = 0

  if (metrics.engagementRate < thresholds.minEngagementRate) {
    flags.push('engagement_too_low')
    score += 25
  } else if (metrics.engagementRate > thresholds.maxEngagementRate) {
    flags.push('engagement_suspiciously_high')
    score += 35
  }

  // Check for perfect engagement rates (bot indicator)
  if (metrics.engagementRate > 0.95) {
    flags.push('perfect_engagement_rate')
    score += 50
  }

  return { score, flags }
}

function analyzeViewEngagementRatio(metrics: SocialMediaMetrics, thresholds: any) {
  const flags: string[] = []
  let score = 0

  if (metrics.views > 0) {
    const viewLikeRatio = metrics.views / (metrics.likes || 1)
    const viewCommentRatio = metrics.views / (metrics.comments || 1)

    // Check view-to-like ratio
    if (viewLikeRatio < thresholds.minViewLikeRatio) {
      flags.push('too_many_likes_for_views')
      score += 30
    } else if (viewLikeRatio > thresholds.maxViewLikeRatio) {
      flags.push('too_few_likes_for_views')
      score += 20
    }

    // Check view-to-comment ratio
    if (viewCommentRatio < thresholds.minViewCommentRatio) {
      flags.push('too_many_comments_for_views')
      score += 25
    } else if (viewCommentRatio > thresholds.maxViewCommentRatio) {
      flags.push('too_few_comments_for_views')
      score += 15
    }
  }

  return { score, flags }
}

function analyzeAccountCredibility(accountInfo: SocialMediaMetrics['accountInfo']) {
  const flags: string[] = []
  let score = 0

  // Check follower count credibility
  if (accountInfo.followerCount < 100) {
    flags.push('very_low_follower_count')
    score += 20
  } else if (accountInfo.followerCount < 1000) {
    flags.push('low_follower_count')
    score += 10
  }

  // Check account age
  if (accountInfo.accountAge < 30) {
    flags.push('very_new_account')
    score += 30
  } else if (accountInfo.accountAge < 90) {
    flags.push('new_account')
    score += 15
  }

  // Verification status bonus
  if (accountInfo.verificationStatus) {
    score = Math.max(0, score - 10) // Reduce fraud score for verified accounts
  } else {
    flags.push('unverified_account')
    score += 5
  }

  return { score, flags }
}

function analyzeTemporalPatterns(metrics: SocialMediaMetrics) {
  const flags: string[] = []
  let score = 0

  // Check posting time patterns (basic implementation)
  const postTime = new Date(metrics.timestamp)
  const hour = postTime.getHours()

  // Suspicious if posted at unusual hours consistently
  if (hour >= 2 && hour <= 5) {
    flags.push('unusual_posting_time')
    score += 10
  }

  // Check for rapid engagement spikes (would need historical data)
  // This is a placeholder for more sophisticated temporal analysis

  return { score, flags }
}

function analyzeCrossPlatformConsistency(metrics: SocialMediaMetrics) {
  const flags: string[] = []
  let score = 0

  // This would compare metrics across platforms for the same user
  // Placeholder for cross-platform analysis
  // Would need access to user's other platform data

  return { score, flags }
}

function getFraudLevel(fraudScore: number): 'safe' | 'warning' | 'suspicious' | 'fraud' {
  if (fraudScore >= 80) return 'fraud'
  if (fraudScore >= 60) return 'suspicious'
  if (fraudScore >= 30) return 'warning'
  return 'safe'
}

function getPayoutDecision(fraudLevel: string, fraudScore: number) {
  switch (fraudLevel) {
    case 'safe':
      return {
        eligible: true,
        multiplier: 1.0,
        delay: 0,
        recommendation: 'Approve payout immediately'
      }
    case 'warning':
      return {
        eligible: true,
        multiplier: 0.9,
        delay: 24,
        recommendation: 'Approve payout with 10% reduction and 24h delay'
      }
    case 'suspicious':
      return {
        eligible: false,
        multiplier: 0.0,
        delay: 168, // 7 days
        recommendation: 'Hold payout for manual review (7 days)'
      }
    case 'fraud':
      return {
        eligible: false,
        multiplier: 0.0,
        delay: -1,
        recommendation: 'Reject payout and ban user'
      }
    default:
      return {
        eligible: false,
        multiplier: 0.0,
        delay: 168,
        recommendation: 'Manual review required'
      }
  }
}

/** Calculate fair payout amount based on fraud analysis */
export function calculateFairPayout(
  baseAmount: number,
  fraudResult: FraudDetectionResult,
  platformMultiplier: number = 1.0
): {
  originalAmount: number
  adjustedAmount: number
  deductions: Array<{ reason: string; amount: number }>
  finalAmount: number
} {
  const deductions: Array<{ reason: string; amount: number }> = []
  let adjustedAmount = baseAmount * platformMultiplier

  // Apply fraud-based multiplier
  if (fraudResult.payoutMultiplier < 1.0) {
    const deduction = adjustedAmount * (1 - fraudResult.payoutMultiplier)
    deductions.push({
      reason: `Fraud risk reduction (${Math.round((1 - fraudResult.payoutMultiplier) * 100)}%)`,
      amount: deduction
    })
    adjustedAmount *= fraudResult.payoutMultiplier
  }

  // Additional deductions based on specific flags
  fraudResult.flags.forEach(flag => {
    const flagDeduction = getFlagDeduction(flag, adjustedAmount)
    if (flagDeduction > 0) {
      deductions.push({
        reason: `${flag.replace(/_/g, ' ')} penalty`,
        amount: flagDeduction
      })
      adjustedAmount -= flagDeduction
    }
  })

  return {
    originalAmount: baseAmount,
    adjustedAmount: baseAmount * platformMultiplier,
    deductions,
    finalAmount: Math.max(0, adjustedAmount)
  }
}

function getFlagDeduction(flag: string, amount: number): number {
  const deductionRates: Record<string, number> = {
    'engagement_too_low': 0.05,
    'engagement_suspiciously_high': 0.15,
    'perfect_engagement_rate': 0.25,
    'too_many_likes_for_views': 0.20,
    'too_many_comments_for_views': 0.15,
    'very_new_account': 0.10,
    'unverified_account': 0.02,
    'unusual_posting_time': 0.03
  }

  return amount * (deductionRates[flag] || 0)
}

function calcAccountAge(days: number) {
  if (days < 7) return 80
  if (days < 30) return 60
  if (days < 90) return 30
  return 0
}

function calcPattern(consistency: number) {
  if (consistency > 0.95) return 80
  if (consistency < 0.1) return 60
  return 0
}
