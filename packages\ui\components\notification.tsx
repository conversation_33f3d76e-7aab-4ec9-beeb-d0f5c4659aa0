"use client"

import * as React from "react"
import { cn } from "../lib/utils"
import { <PERSON><PERSON> } from "./button"
import { Badge } from "./badge"
import { Icons } from "./icons"

export interface AppNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error' | 'campaign' | 'chat' | 'payment'
  read: boolean
  created_at: string
  action_url?: string
  action_text?: string
  user_id: string
  campaign_id?: string
  metadata?: Record<string, any>
}

interface NotificationItemProps {
  notification: AppNotification
  onMarkAsRead: (id: string) => void
  onAction?: (notification: Notification) => void
  className?: string
}

const NotificationItem = React.forwardRef<HTMLDivElement, NotificationItemProps>(
  ({ notification, onMarkAsRead, onAction, className }, ref) => {
    const getIcon = () => {
      switch (notification.type) {
        case 'success':
          return <Icons.checkCircle className="h-5 w-5 text-green-500" />
        case 'warning':
          return <Icons.alertTriangle className="h-5 w-5 text-yellow-500" />
        case 'error':
          return <Icons.alertCircle className="h-5 w-5 text-red-500" />
        case 'campaign':
          return <Icons.megaphone className="h-5 w-5 text-blue-500" />
        case 'chat':
          return <Icons.messageCircle className="h-5 w-5 text-purple-500" />
        case 'payment':
          return <Icons.dollarSign className="h-5 w-5 text-green-500" />
        default:
          return <Icons.bell className="h-5 w-5 text-gray-500" />
      }
    }

    const formatTime = (timestamp: string) => {
      const date = new Date(timestamp)
      const now = new Date()
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
      
      if (diffInMinutes < 1) return 'Just now'
      if (diffInMinutes < 60) return `${diffInMinutes}m ago`
      
      const diffInHours = Math.floor(diffInMinutes / 60)
      if (diffInHours < 24) return `${diffInHours}h ago`
      
      const diffInDays = Math.floor(diffInHours / 24)
      if (diffInDays < 7) return `${diffInDays}d ago`
      
      return date.toLocaleDateString()
    }

    return (
      <div
        ref={ref}
        className={cn(
          "flex gap-3 p-4 border-b hover:bg-muted/50 transition-colors",
          !notification.read && "bg-blue-50/50 border-l-4 border-l-blue-500",
          className
        )}
      >
        <div className="flex-shrink-0 mt-1">
          {getIcon()}
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1">
              <h4 className={cn(
                "text-sm font-medium",
                !notification.read && "font-semibold"
              )}>
                {notification.title}
              </h4>
              <p className="text-sm text-muted-foreground mt-1">
                {notification.message}
              </p>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground whitespace-nowrap">
                {formatTime(notification.created_at)}
              </span>
              {!notification.read && (
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
              )}
            </div>
          </div>
          
          {notification.action_url && notification.action_text && (
            <div className="mt-2">
              <Button
                variant="outline"
                size="sm"
                // @ts-ignore - Avoiding conflict with browser Notification API
                onClick={() => onAction?.(notification)}
              >
                {notification.action_text}
              </Button>
            </div>
          )}
        </div>
        
        {!notification.read && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onMarkAsRead(notification.id)}
            className="flex-shrink-0"
          >
            <Icons.check className="h-4 w-4" />
          </Button>
        )}
      </div>
    )
  }
)

NotificationItem.displayName = "NotificationItem"

interface NotificationListProps {
  notifications: AppNotification[]
  onMarkAsRead: (id: string) => void
  onMarkAllAsRead: () => void
  onAction?: (notification: AppNotification) => void
  loading?: boolean
  className?: string
}

const NotificationList = React.forwardRef<HTMLDivElement, NotificationListProps>(
  ({ notifications, onMarkAsRead, onMarkAllAsRead, onAction, loading, className }, ref) => {
    const unreadCount = notifications.filter(n => !n.read).length

    return (
      <div ref={ref} className={cn("w-full", className)}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <h3 className="font-semibold">Notifications</h3>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="text-xs">
                {unreadCount} new
              </Badge>
            )}
          </div>
          
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onMarkAllAsRead}
              disabled={loading}
            >
              Mark all as read
            </Button>
          )}
        </div>

        {/* Notifications */}
        <div className="max-h-96 overflow-y-auto">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : notifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center p-8 text-center">
              <Icons.bell className="h-8 w-8 text-muted-foreground mb-2" />
              <p className="text-sm text-muted-foreground">No notifications yet</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <NotificationItem
                key={notification.id}
                notification={notification}
                onMarkAsRead={onMarkAsRead}
                onAction={onAction}
              />
            ))
          )}
        </div>
      </div>
    )
  }
)

NotificationList.displayName = "NotificationList"

interface NotificationBellProps {
  unreadCount: number
  onClick: () => void
  className?: string
}

const NotificationBell = React.forwardRef<HTMLButtonElement, NotificationBellProps>(
  ({ unreadCount, onClick, className }, ref) => {
    return (
      <Button
        ref={ref}
        variant="ghost"
        size="icon"
        onClick={onClick}
        className={cn("relative", className)}
      >
        <Icons.bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge
            variant="destructive"
            className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs flex items-center justify-center"
          >
            {unreadCount > 99 ? '99+' : unreadCount}
          </Badge>
        )}
      </Button>
    )
  }
)

NotificationBell.displayName = "NotificationBell"

export { NotificationItem, NotificationList, NotificationBell }
export type { AppNotification }
