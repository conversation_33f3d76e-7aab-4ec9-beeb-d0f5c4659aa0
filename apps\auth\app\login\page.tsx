"use client"

import { createSupabaseBrowserClient } from "@creatorboost/database"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import type { SupabaseClient, Provider } from "@supabase/supabase-js"
import { Button, Card, CardContent, CardDescription, CardHeader, CardTitle, Icons } from "@creatorboost/ui";

export default function LoginPage() {
  const router = useRouter()
  const [supabase] = useState<SupabaseClient>(() => createSupabaseBrowserClient())

  // Handle OAuth login
  const handleLogin = async (provider: Provider) => {
    await supabase.auth.signInWithOAuth({
      provider,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_AUTH_URL}/auth/callback`,
      },
    })
  }

  // Redirect user if they are already logged in
  useEffect(() => {
    const getSession = async () => {
      const { data: { session } } = await supabase.auth.getSession();
      if (session) {
        // Logic to redirect based on role, similar to before
        const { data: profile } = await supabase
          .from('users')
          .select('role')
          .eq('id', session.user.id)
          .single();
        
        if (profile?.role === 'admin') {
          router.push(process.env.NEXT_PUBLIC_ADMIN_URL!);
        } else {
          router.push(process.env.NEXT_PUBLIC_DASHBOARD_URL!);
        }
      }
    };
    getSession();
  }, [router, supabase])

  return (
    <div className="min-h-screen bg-background flex items-center justify-center">
      <Card className="w-full max-w-sm mx-auto">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
          <CardDescription>
            Sign in to access your dashboard and manage your campaigns.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button variant="outline" className="w-full" onClick={() => handleLogin('google')}>
              <Icons.google className="mr-2 h-4 w-4" />
              Continue with Google
            </Button>
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t" />
              </div>
              <div className="relative flex justify-center text-xs uppercase">
                <span className="bg-background px-2 text-muted-foreground">
                  Social Media Platforms
                </span>
              </div>
            </div>
            <Button variant="outline" className="w-full" onClick={() => handleLogin('google')}>
              <Icons.tiktok className="mr-2 h-4 w-4" />
              Connect TikTok Account
            </Button>
            <Button variant="outline" className="w-full" onClick={() => handleLogin('google')}>
              <Icons.instagram className="mr-2 h-4 w-4" />
              Connect Instagram Account
            </Button>
            <Button variant="outline" className="w-full" onClick={() => handleLogin('google')}>
              <Icons.youtube className="mr-2 h-4 w-4" />
              Connect YouTube Account
            </Button>
          </div>
          <div className="mt-6 text-center text-sm text-muted-foreground">
            <p>Connect your social media accounts to track views, likes, and engagement for accurate payout calculations.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
