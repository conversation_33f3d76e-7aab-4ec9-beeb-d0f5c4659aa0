"use client"

import { useState, useEffect } from 'react'
import { useAuth, useChat } from '@creatorboost/hooks'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  Button,
  Input,
  Badge,
  Chat,
  Icons
} from '@creatorboost/ui'

export default function ChatPage() {
  const { user, loading: authLoading } = useAuth()
  const [selectedRoomId, setSelectedRoomId] = useState<string | null>(null)
  const [showCreateRoom, setShowCreateRoom] = useState(false)
  const [newRoomName, setNewRoomName] = useState('')
  const [newRoomDescription, setNewRoomDescription] = useState('')

  const {
    messages,
    rooms,
    currentRoom,
    loading,
    error,
    isConnected,
    sendMessage,
    createRoom,
    joinRoom,
    clearError
  } = useChat(selectedRoomId || undefined)

  useEffect(() => {
    if (rooms.length > 0 && !selectedRoomId) {
      setSelectedRoomId(rooms[0].id)
    }
  }, [rooms, selectedRoomId])

  const handleSendMessage = async (content: string, type?: 'text' | 'image' | 'file') => {
    if (!selectedRoomId || !user) return
    
    try {
      await sendMessage(selectedRoomId, content, type)
    } catch (err) {
      console.error('Failed to send message:', err)
    }
  }

  const handleCreateRoom = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newRoomName.trim()) return

    try {
      const room = await createRoom(newRoomName.trim(), newRoomDescription.trim())
      setSelectedRoomId(room.id)
      setNewRoomName('')
      setNewRoomDescription('')
      setShowCreateRoom(false)
    } catch (err) {
      console.error('Failed to create room:', err)
    }
  }

  if (authLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="flex items-center justify-center h-screen">
        <Card className="w-full max-w-md">
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Please log in to access the chat feature.
            </p>
            <Button asChild className="w-full">
              <a href="/auth/login">Sign In</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 h-screen flex flex-col">
      <div className="mb-6">
        <h1 className="text-3xl font-bold">Team Chat</h1>
        <p className="text-muted-foreground">
          Collaborate with your team in real-time
        </p>
      </div>

      {error && (
        <div className="mb-4 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
          <div className="flex items-center justify-between">
            <p className="text-destructive">{error}</p>
            <Button variant="ghost" size="sm" onClick={clearError}>
              <Icons.x className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      <div className="flex-1 grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Room List Sidebar */}
        <div className="lg:col-span-1">
          <Card className="h-full">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Chat Rooms</CardTitle>
                <Button 
                  size="sm" 
                  onClick={() => setShowCreateRoom(true)}
                  disabled={loading}
                >
                  <Icons.plus className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-0">
              <div className="space-y-1">
                {rooms.map((room) => (
                  <button
                    key={room.id}
                    onClick={() => setSelectedRoomId(room.id)}
                    className={`w-full text-left p-3 hover:bg-muted/50 transition-colors ${
                      selectedRoomId === room.id ? 'bg-muted' : ''
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Icons.messageCircle className="h-4 w-4" />
                        <span className="font-medium truncate">{room.name}</span>
                      </div>
                      <Badge variant="secondary" className="text-xs">
                        {room.participant_count}
                      </Badge>
                    </div>
                    {room.description && (
                      <p className="text-sm text-muted-foreground mt-1 truncate">
                        {room.description}
                      </p>
                    )}
                  </button>
                ))}
                
                {rooms.length === 0 && !loading && (
                  <div className="p-4 text-center text-muted-foreground">
                    <Icons.messageCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
                    <p className="text-sm">No chat rooms yet</p>
                    <p className="text-xs">Create one to get started</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Chat Area */}
        <div className="lg:col-span-3">
          <Card className="h-full flex flex-col">
            {currentRoom ? (
              <>
                <CardHeader className="pb-3 border-b">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-lg">{currentRoom.name}</CardTitle>
                      {currentRoom.description && (
                        <p className="text-sm text-muted-foreground">
                          {currentRoom.description}
                        </p>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={isConnected ? "default" : "secondary"}>
                        {isConnected ? "Connected" : "Connecting..."}
                      </Badge>
                      <Badge variant="outline">
                        {currentRoom.participant_count} members
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                
                <div className="flex-1 min-h-0">
                  <Chat
                    messages={messages}
                    currentUserId={user.id}
                    onSendMessage={handleSendMessage}
                    isLoading={loading}
                    className="h-full"
                  />
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center">
                  <Icons.messageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Select a chat room</h3>
                  <p className="text-muted-foreground">
                    Choose a room from the sidebar to start chatting
                  </p>
                </div>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* Create Room Modal */}
      {showCreateRoom && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle>Create New Chat Room</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleCreateRoom} className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Room Name</label>
                  <Input
                    value={newRoomName}
                    onChange={(e) => setNewRoomName(e.target.value)}
                    placeholder="Enter room name"
                    required
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Description (Optional)</label>
                  <Input
                    value={newRoomDescription}
                    onChange={(e) => setNewRoomDescription(e.target.value)}
                    placeholder="Enter room description"
                  />
                </div>
                <div className="flex gap-2 pt-4">
                  <Button type="submit" disabled={!newRoomName.trim() || loading}>
                    Create Room
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setShowCreateRoom(false)}
                  >
                    Cancel
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
