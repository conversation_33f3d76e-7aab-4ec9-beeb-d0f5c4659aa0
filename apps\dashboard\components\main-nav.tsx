
"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import { cn, Icons } from "@creatorboost/ui";

export function MainNav({
  className,
  ...props
}: React.HTMLAttributes<HTMLElement>) {
  const pathname = usePathname()

  const routes = [
    {
      href: "/dashboard",
      label: "Overview",
      active: pathname === "/dashboard",
    },
    {
      href: "/dashboard/campaigns",
      label: "Campaigns",
      active: pathname.startsWith("/dashboard/campaigns"),
    },
    {
      href: "/dashboard/analytics",
      label: "Analytics",
      active: pathname.startsWith("/dashboard/analytics"),
    },
    {
      href: "/chat",
      label: "Chat",
      active: pathname.startsWith("/chat"),
    },
    {
        href: "/dashboard/earnings",
        label: "Earnings",
        active: pathname.startsWith("/dashboard/earnings"),
    },
    {
      href: "/dashboard/settings",
      label: "Settings",
      active: pathname.startsWith("/dashboard/settings"),
    },
  ]

  return (
    <nav
      className={cn("flex items-center space-x-4 lg:space-x-6", className)}
      {...props}
    >
      {routes.map((route) => (
        <Link
          key={route.href}
          href={route.href}
          className={cn(
            "text-sm font-medium transition-colors hover:text-primary",
            route.active ? "text-black dark:text-white" : "text-muted-foreground"
          )}
        >
          {route.label}
        </Link>
      ))}
    </nav>
  )
}
