import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "../../../styles/globals.css";
// import { ThemeProvider } from "@creatorboost/ui";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Creator Booster - Connect, Promote, Earn",
  description: "The ultimate platform connecting content creators with skilled promoters.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}