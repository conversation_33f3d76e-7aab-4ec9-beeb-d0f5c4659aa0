[04:39:00.780] Running build in Washington, D.C., USA (East) – iad1
[04:39:00.780] Build machine configuration: 2 cores, 8 GB
[04:39:00.796] Cloning github.com/rendoarsandi/creatorboost (Branch: main, Commit: d6667a2)
[04:39:01.007] Previous build caches not available
[04:39:01.387] Cloning completed: 590.000ms
[04:39:01.697] Running "vercel build"
[04:39:02.316] Vercel CLI 43.3.0
[04:39:02.473] > Detected Turbo. Adjusting default settings...
[04:39:02.632] Running "install" command: `npm install`...
[04:39:05.243] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[04:39:05.853] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[04:39:05.994] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[04:39:07.249] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[04:39:07.308] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[04:39:09.668] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[04:39:16.663] 
[04:39:16.664] added 448 packages, and audited 452 packages in 14s
[04:39:16.664] 
[04:39:16.665] 140 packages are looking for funding
[04:39:16.665]   run `npm fund` for details
[04:39:16.665] 
[04:39:16.665] found 0 vulnerabilities
[04:39:16.723] Detected Next.js version: 15.3.4
[04:39:16.724] Running "cd ../.. && npx turbo run build --filter=@creatorboost/auth"
[04:39:17.629] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[04:39:19.959] 
[04:39:19.960] Attention:
[04:39:19.960] Turborepo now collects completely anonymous telemetry regarding usage.
[04:39:19.960] This information is used to shape the Turborepo roadmap and prioritize features.
[04:39:19.961] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:39:19.961] https://turbo.build/repo/docs/telemetry
[04:39:19.961] 
[04:39:19.998] • Packages in scope: @creatorboost/auth
[04:39:19.999] • Running build in 1 packages
[04:39:19.999] • Remote caching enabled
[04:39:20.123] @creatorboost/auth:build: cache miss, executing ff9f19f50b81a36b
[04:39:20.262] @creatorboost/auth:build: 
[04:39:20.265] @creatorboost/auth:build: > @creatorboost/auth@0.1.0 build
[04:39:20.265] @creatorboost/auth:build: > next build
[04:39:20.265] @creatorboost/auth:build: 
[04:39:20.911] @creatorboost/auth:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[04:39:20.911] @creatorboost/auth:build: This information is used to shape Next.js' roadmap and prioritize features.
[04:39:20.911] @creatorboost/auth:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:39:20.912] @creatorboost/auth:build: https://nextjs.org/telemetry
[04:39:20.912] @creatorboost/auth:build: 
[04:39:20.975] @creatorboost/auth:build:    ▲ Next.js 15.3.4
[04:39:20.975] @creatorboost/auth:build: 
[04:39:21.041] @creatorboost/auth:build:    Creating an optimized production build ...
[04:39:29.584] @creatorboost/auth:build: <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (100kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[04:39:34.449] @creatorboost/auth:build:  ✓ Compiled successfully in 9.0s
[04:39:34.475] @creatorboost/auth:build:    Linting and checking validity of types ...
[04:39:38.867] @creatorboost/auth:build: Failed to compile.
[04:39:38.867] @creatorboost/auth:build: 
[04:39:38.867] @creatorboost/auth:build: ../../packages/database/lib/supabase.ts:2:30
[04:39:38.867] @creatorboost/auth:build: Type error: Cannot find module 'next/headers' or its corresponding type declarations.
[04:39:38.867] @creatorboost/auth:build: 
[04:39:38.867] @creatorboost/auth:build: [0m [90m 1 |[39m [36mimport[39m { createBrowserClient[33m,[39m createServerClient[33m,[39m type [33mCookieOptions[39m } [36mfrom[39m [32m'@supabase/ssr'[39m[0m
[04:39:38.867] @creatorboost/auth:build: [0m[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { type cookies } [36mfrom[39m [32m'next/headers'[39m[0m
[04:39:38.867] @creatorboost/auth:build: [0m [90m   |[39m                              [31m[1m^[22m[39m[0m
[04:39:38.868] @creatorboost/auth:build: [0m [90m 3 |[39m[0m
[04:39:38.868] @creatorboost/auth:build: [0m [90m 4 |[39m [90m// Define a function to create a Supabase client for server-side operations[39m[0m
[04:39:38.868] @creatorboost/auth:build: [0m [90m 5 |[39m [36mexport[39m [36mfunction[39m createSupabaseServerClient(cookieStore[33m:[39m [33mReturnType[39m[33m<[39m[36mtypeof[39m cookies[33m>[39m) {[0m
[04:39:38.884] @creatorboost/auth:build: Next.js build worker exited with code: 1 and signal: null
[04:39:38.892] @creatorboost/auth:build: npm error Lifecycle script `build` failed with error:
[04:39:38.892] @creatorboost/auth:build: npm error code 1
[04:39:38.892] @creatorboost/auth:build: npm error path /vercel/path0/apps/auth
[04:39:38.893] @creatorboost/auth:build: npm error workspace @creatorboost/auth@0.1.0
[04:39:38.893] @creatorboost/auth:build: npm error location /vercel/path0/apps/auth
[04:39:38.893] @creatorboost/auth:build: npm error command failed
[04:39:38.893] @creatorboost/auth:build: npm error command sh -c next build
[04:39:38.897] @creatorboost/auth:build: ERROR: command finished with error: command (/vercel/path0/apps/auth) /node22/bin/npm run build exited (1)
[04:39:38.898] @creatorboost/auth#build: command (/vercel/path0/apps/auth) /node22/bin/npm run build exited (1)
[04:39:38.898] 
[04:39:38.898]   Tasks:    0 successful, 1 total
[04:39:38.898]  Cached:    0 cached, 1 total
[04:39:38.899]    Time:    18.934s 
[04:39:38.899] Summary:    /vercel/path0/.turbo/runs/2zARdTpFAl7WHnEB56peeHItq68.json
[04:39:38.899]  Failed:    @creatorboost/auth#build
[04:39:38.899] 
[04:39:38.901]  ERROR  run failed: command  exited (1)
[04:39:38.980] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/auth" exited with 1
[04:39:39.252] 
[04:39:42.973] Exiting build container