[06:02:57.072] Running build in Washington, D.C., USA (East) – iad1
[06:02:57.073] Build machine configuration: 2 cores, 8 GB
[06:02:57.086] Cloning github.com/rendoarsandi/creatorboost (Branch: feature/core-patch, Commit: ab1ae55)
[06:02:57.380] Previous build caches not available
[06:02:57.555] Cloning completed: 469.000ms
[06:02:58.053] Running "vercel build"
[06:02:58.493] Vercel CLI 43.3.0
[06:02:58.651] > Detected Turbo. Adjusting default settings...
[06:02:58.802] Running "install" command: `npm install`...
[06:03:01.655] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[06:03:02.053] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[06:03:02.172] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:03:03.470] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[06:03:03.497] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[06:03:06.050] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[06:03:12.960] 
[06:03:12.961] added 465 packages, and audited 469 packages in 14s
[06:03:12.962] 
[06:03:12.962] 140 packages are looking for funding
[06:03:12.962]   run `npm fund` for details
[06:03:12.963] 
[06:03:12.963] 2 low severity vulnerabilities
[06:03:12.963] 
[06:03:12.964] To address all issues (including breaking changes), run:
[06:03:12.964]   npm audit fix --force
[06:03:12.964] 
[06:03:12.964] Run `npm audit` for details.
[06:03:13.027] Detected Next.js version: 15.3.4
[06:03:13.028] Running "cd ../.. && npx turbo run build --filter=@creatorboost/auth"
[06:03:13.998] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[06:03:16.374] 
[06:03:16.374] Attention:
[06:03:16.374] Turborepo now collects completely anonymous telemetry regarding usage.
[06:03:16.375] This information is used to shape the Turborepo roadmap and prioritize features.
[06:03:16.375] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:03:16.375] https://turbo.build/repo/docs/telemetry
[06:03:16.375] 
[06:03:16.411] • Packages in scope: @creatorboost/auth
[06:03:16.412] • Running build in 1 packages
[06:03:16.412] • Remote caching enabled
[06:03:16.535] @creatorboost/auth:build: cache miss, executing cdf29b1efc33d1d3
[06:03:16.713] @creatorboost/auth:build: 
[06:03:16.714] @creatorboost/auth:build: > @creatorboost/auth@0.1.0 build
[06:03:16.714] @creatorboost/auth:build: > next build
[06:03:16.714] @creatorboost/auth:build: 
[06:03:17.366] @creatorboost/auth:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[06:03:17.367] @creatorboost/auth:build: This information is used to shape Next.js' roadmap and prioritize features.
[06:03:17.367] @creatorboost/auth:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:03:17.367] @creatorboost/auth:build: https://nextjs.org/telemetry
[06:03:17.368] @creatorboost/auth:build: 
[06:03:17.430] @creatorboost/auth:build:    ▲ Next.js 15.3.4
[06:03:17.431] @creatorboost/auth:build: 
[06:03:17.498] @creatorboost/auth:build:    Creating an optimized production build ...
[06:03:26.444] @creatorboost/auth:build: <w> [webpack.cache.PackFileCacheStrategy] Serializing big strings (100kiB) impacts deserialization performance (consider using Buffer instead and decode when needed)
[06:03:31.616] @creatorboost/auth:build:  ✓ Compiled successfully in 11.0s
[06:03:31.620] @creatorboost/auth:build:    Linting and checking validity of types ...
[06:03:35.814] @creatorboost/auth:build: Failed to compile.
[06:03:35.815] @creatorboost/auth:build: 
[06:03:35.815] @creatorboost/auth:build: ../../packages/database/lib/supabase.ts:2:25
[06:03:35.815] @creatorboost/auth:build: Type error: Cannot find module 'next/headers' or its corresponding type declarations.
[06:03:35.816] @creatorboost/auth:build: 
[06:03:35.816] @creatorboost/auth:build: [0m [90m 1 |[39m [36mimport[39m { createBrowserClient[33m,[39m createServerClient[33m,[39m type [33mCookieOptions[39m } [36mfrom[39m [32m'@supabase/ssr'[39m[0m
[06:03:35.816] @creatorboost/auth:build: [0m[31m[1m>[22m[39m[90m 2 |[39m [36mimport[39m { cookies } [36mfrom[39m [32m'next/headers'[39m[0m
[06:03:35.816] @creatorboost/auth:build: [0m [90m   |[39m                         [31m[1m^[22m[39m[0m
[06:03:35.817] @creatorboost/auth:build: [0m [90m 3 |[39m[0m
[06:03:35.817] @creatorboost/auth:build: [0m [90m 4 |[39m [90m// Define a function to create a Supabase client for server-side operations[39m[0m
[06:03:35.817] @creatorboost/auth:build: [0m [90m 5 |[39m [36mexport[39m [36mfunction[39m createSupabaseServerClient(cookieStore[33m:[39m [33mReturnType[39m[33m<[39m[36mtypeof[39m cookies[33m>[39m) {[0m
[06:03:35.831] @creatorboost/auth:build: Next.js build worker exited with code: 1 and signal: null
[06:03:35.837] @creatorboost/auth:build: npm error Lifecycle script `build` failed with error:
[06:03:35.838] @creatorboost/auth:build: npm error code 1
[06:03:35.838] @creatorboost/auth:build: npm error path /vercel/path0/apps/auth
[06:03:35.838] @creatorboost/auth:build: npm error workspace @creatorboost/auth@0.1.0
[06:03:35.838] @creatorboost/auth:build: npm error location /vercel/path0/apps/auth
[06:03:35.838] @creatorboost/auth:build: npm error command failed
[06:03:35.838] @creatorboost/auth:build: npm error command sh -c next build
[06:03:35.843] @creatorboost/auth:build: ERROR: command finished with error: command (/vercel/path0/apps/auth) /node22/bin/npm run build exited (1)
[06:03:35.844] @creatorboost/auth#build: command (/vercel/path0/apps/auth) /node22/bin/npm run build exited (1)
[06:03:35.844] 
[06:03:35.845]   Tasks:    0 successful, 1 total
[06:03:35.845]  Cached:    0 cached, 1 total
[06:03:35.845]    Time:    19.466s 
[06:03:35.845] Summary:    /vercel/path0/.turbo/runs/2zAbqT0b33FJW667zp5PUH1fRxN.json
[06:03:35.845]  Failed:    @creatorboost/auth#build
[06:03:35.845] 
[06:03:35.851]  ERROR  run failed: command  exited (1)
[06:03:35.930] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/auth" exited with 1
[06:03:36.206] 
[06:03:39.123] Exiting build container