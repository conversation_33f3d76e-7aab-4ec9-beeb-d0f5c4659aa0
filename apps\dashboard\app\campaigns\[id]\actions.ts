
"use server"

import { createSupabaseServerClient } from "@creatorboost/database";
import { cookies } from "next/headers";
import { revalidatePath } from "next/cache";

export async function createSubmission(formData: FormData) {
  const cookieStore = await cookies();
  const supabase = createSupabaseServerClient(cookieStore);

  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return { error: "You must be logged in to submit." };
  }

  const postUrl = formData.get("postUrl") as string;
  const campaignId = formData.get("campaignId") as string;

  if (!postUrl || !campaignId) {
    return { error: "Post URL and Campaign ID are required." };
  }

  const { error } = await supabase.from("submissions").insert({
    post_url: postUrl,
    campaign_id: campaignId,
    promoter_id: user.id,
    status: "pending", // Default status
  });

  if (error) {
    console.error("Error creating submission:", error);
    return { error: "Failed to create submission." };
  }

  revalidatePath(`/dashboard/campaigns/${campaignId}`);
  return { success: "Submission created successfully!" };
}
