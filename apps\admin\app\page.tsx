
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@creatorboost/ui/card";
import { Icons } from "@creatorboost/ui/icons";
import { createSupabaseServerClient } from "@creatorboost/database"
import { cookies } from "next/headers"
  
async function getAdminStats() {
    const cookieStore = await cookies();
    const supabase = createSupabaseServerClient(cookieStore);

    const { count: userCount } = await supabase.from('profiles').select('*', { count: 'exact', head: true });
    const { count: campaignCount } = await supabase.from('campaigns').select('*', { count: 'exact', head: true });
    const { data: pendingPayouts, error } = await supabase.from('submissions').select('payout_amount').eq('status', 'pending');

    const totalPending = pendingPayouts?.reduce((acc, curr) => acc + (curr.payout_amount || 0), 0) || 0;

    return { userCount, campaignCount, totalPending };
}

export default async function AdminDashboardPage() {
    const { userCount, campaignCount, totalPending } = await getAdminStats();

    return (
        <>
            <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-3">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Total Users
                        </CardTitle>
                        <Icons.users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{userCount ?? '0'}</div>
                        <p className="text-xs text-muted-foreground">
                            All registered users
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">
                            Total Campaigns
                        </CardTitle>
                        <Icons.billing className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{campaignCount ?? '0'}</div>
                        <p className="text-xs text-muted-foreground">
                            Across all statuses
                        </p>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Pending Payouts</CardTitle>
                        <Icons.dollarSign className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">${totalPending.toFixed(2)}</div>
                        <p className="text-xs text-muted-foreground">
                            Awaiting admin approval
                        </p>
                    </CardContent>
                </Card>
            </div>
            {/* Here you can add more components like recent users table etc. */}
        </>
    )
}
