// supabase/functions/cron-scraper/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

serve(async () => {
  try {
    // 1. Fetch all active submissions
    const { data: submissions, error: submissionsError } = await supabase
      .from("submissions")
      .select("id, post_url, created_at") // Select created_at
      .in("status", ["pending", "approved"])

    if (submissionsError) {
      throw new Error(`Failed to fetch submissions: ${submissionsError.message}`)
    }

    if (!submissions || submissions.length === 0) {
      return new Response("No active submissions to process.", { status: 200 })
    }

    // 2. Invoke the metric-scraper function for each submission
    const scrapePromises = submissions.map(submission =>
      supabase.functions.invoke("metric-scraper", {
        body: {
          submission_id: submission.id,
          post_url: submission.post_url,
          created_at: submission.created_at, // Pass created_at
        },
      })
    )

    const results = await Promise.all(scrapePromises)

    results.forEach(result => {
      if (result.error) {
        console.error(`Error invoking metric-scraper:`, result.error.message)
      }
    })

    return new Response(JSON.stringify({ success: true, processed: results.length }), {
      headers: { "Content-Type": "application/json" },
    })

  } catch (error) {
    console.error("Cron job failed:", error.message)
    return new Response(error.message, { status: 500 })
  }
})
