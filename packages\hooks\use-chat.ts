"use client"

import { useState, useEffect, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

export interface ChatMessage {
  id: string
  content: string
  user_id: string
  user_name: string
  user_avatar?: string
  created_at: string
  message_type: 'text' | 'image' | 'file'
  file_url?: string
  file_name?: string
  campaign_id?: string
  room_id: string
}

export interface ChatRoom {
  id: string
  name: string
  description?: string
  campaign_id?: string
  created_by: string
  created_at: string
  is_private: boolean
  participant_count: number
}

export function useChat(roomId?: string) {
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [rooms, setRooms] = useState<ChatRoom[]>([])
  const [currentRoom, setCurrentRoom] = useState<ChatRoom | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [isConnected, setIsConnected] = useState(false)

  // Load chat rooms
  const loadRooms = useCallback(async () => {
    try {
      setLoading(true)
      const { data: session } = await supabase.auth.getSession()
      
      if (!session.session?.user) {
        throw new Error('User not authenticated')
      }

      // Get rooms where user is a participant
      const { data: roomData, error: roomError } = await supabase
        .from('chat_rooms')
        .select(`
          *,
          chat_participants!inner(user_id)
        `)
        .eq('chat_participants.user_id', session.session.user.id)
        .order('created_at', { ascending: false })

      if (roomError) throw roomError

      // Get participant counts for each room
      const roomsWithCounts = await Promise.all(
        (roomData || []).map(async (room) => {
          const { count } = await supabase
            .from('chat_participants')
            .select('*', { count: 'exact', head: true })
            .eq('room_id', room.id)

          return {
            ...room,
            participant_count: count || 0
          }
        })
      )

      setRooms(roomsWithCounts)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load rooms')
    } finally {
      setLoading(false)
    }
  }, [])

  // Load messages for a specific room
  const loadMessages = useCallback(async (roomId: string, limit = 50) => {
    try {
      setLoading(true)
      
      const { data: messageData, error: messageError } = await supabase
        .from('chat_messages')
        .select(`
          *,
          users!chat_messages_user_id_fkey(
            id,
            full_name,
            avatar_url
          )
        `)
        .eq('room_id', roomId)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (messageError) throw messageError

      const formattedMessages: ChatMessage[] = (messageData || []).map(msg => ({
        id: msg.id,
        content: msg.content,
        user_id: msg.user_id,
        user_name: msg.users?.full_name || 'Unknown User',
        user_avatar: msg.users?.avatar_url,
        created_at: msg.created_at,
        message_type: msg.message_type,
        file_url: msg.file_url,
        file_name: msg.file_name,
        campaign_id: msg.campaign_id,
        room_id: msg.room_id
      })).reverse()

      setMessages(formattedMessages)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load messages')
    } finally {
      setLoading(false)
    }
  }, [])

  // Upload file to Supabase Storage
  const uploadFile = useCallback(async (file: File, roomId: string) => {
    try {
      const { data: session } = await supabase.auth.getSession()

      if (!session.session?.user) {
        throw new Error('User not authenticated')
      }

      // Create unique filename with timestamp and user ID
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${session.session.user.id}.${fileExt}`
      const filePath = `chat-files/${roomId}/${fileName}`

      // Upload file to storage
      const { data: uploadData, error: uploadError } = await supabase.storage
        .from('chat-files')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (uploadError) throw uploadError

      // Get public URL
      const { data: urlData } = supabase.storage
        .from('chat-files')
        .getPublicUrl(filePath)

      return {
        url: urlData.publicUrl,
        fileName: file.name,
        filePath: filePath
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to upload file')
      throw err
    }
  }, [])

  // Send a message
  const sendMessage = useCallback(async (
    roomId: string,
    content: string,
    messageType: 'text' | 'image' | 'file' = 'text',
    fileUrl?: string,
    fileName?: string
  ) => {
    try {
      const { data: session } = await supabase.auth.getSession()

      if (!session.session?.user) {
        throw new Error('User not authenticated')
      }

      const { error } = await supabase
        .from('chat_messages')
        .insert({
          room_id: roomId,
          user_id: session.session.user.id,
          content,
          message_type: messageType,
          file_url: fileUrl,
          file_name: fileName
        })

      if (error) throw error
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send message')
      throw err
    }
  }, [])

  // Send file message (upload file and send message)
  const sendFileMessage = useCallback(async (roomId: string, file: File) => {
    try {
      setLoading(true)

      // Upload file first
      const { url, fileName } = await uploadFile(file, roomId)

      // Determine message type based on file type
      const messageType = file.type.startsWith('image/') ? 'image' : 'file'

      // Send message with file info
      await sendMessage(roomId, fileName, messageType, url, fileName)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to send file')
      throw err
    } finally {
      setLoading(false)
    }
  }, [uploadFile, sendMessage])

  // Create a new chat room
  const createRoom = useCallback(async (
    name: string, 
    description?: string, 
    campaignId?: string,
    isPrivate = false
  ) => {
    try {
      const { data: session } = await supabase.auth.getSession()
      
      if (!session.session?.user) {
        throw new Error('User not authenticated')
      }

      const { data: roomData, error: roomError } = await supabase
        .from('chat_rooms')
        .insert({
          name,
          description,
          campaign_id: campaignId,
          created_by: session.session.user.id,
          is_private: isPrivate
        })
        .select()
        .single()

      if (roomError) throw roomError

      // Add creator as participant
      const { error: participantError } = await supabase
        .from('chat_participants')
        .insert({
          room_id: roomData.id,
          user_id: session.session.user.id,
          role: 'admin'
        })

      if (participantError) throw participantError

      await loadRooms()
      return roomData
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create room')
      throw err
    }
  }, [loadRooms])

  // Join a chat room
  const joinRoom = useCallback(async (roomId: string) => {
    try {
      const { data: session } = await supabase.auth.getSession()
      
      if (!session.session?.user) {
        throw new Error('User not authenticated')
      }

      const { error } = await supabase
        .from('chat_participants')
        .insert({
          room_id: roomId,
          user_id: session.session.user.id,
          role: 'member'
        })

      if (error && !error.message.includes('duplicate')) {
        throw error
      }

      await loadRooms()
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join room')
      throw err
    }
  }, [loadRooms])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!roomId) return

    setIsConnected(true)

    // Subscribe to new messages in the current room
    const messageSubscription = supabase
      .channel(`room-${roomId}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'chat_messages',
          filter: `room_id=eq.${roomId}`
        },
        async (payload) => {
          // Fetch user details for the new message
          const { data: userData } = await supabase
            .from('users')
            .select('id, full_name, avatar_url')
            .eq('id', payload.new.user_id)
            .single()

          const newMessage: ChatMessage = {
            id: payload.new.id,
            content: payload.new.content,
            user_id: payload.new.user_id,
            user_name: userData?.full_name || 'Unknown User',
            user_avatar: userData?.avatar_url,
            created_at: payload.new.created_at,
            message_type: payload.new.message_type,
            file_url: payload.new.file_url,
            file_name: payload.new.file_name,
            campaign_id: payload.new.campaign_id,
            room_id: payload.new.room_id
          }

          setMessages(prev => [...prev, newMessage])
        }
      )
      .subscribe()

    return () => {
      messageSubscription.unsubscribe()
      setIsConnected(false)
    }
  }, [roomId])

  // Load initial data
  useEffect(() => {
    loadRooms()
  }, [loadRooms])

  // Load messages when room changes
  useEffect(() => {
    if (roomId) {
      loadMessages(roomId)
      
      // Find and set current room
      const room = rooms.find(r => r.id === roomId)
      setCurrentRoom(room || null)
    }
  }, [roomId, loadMessages, rooms])

  return {
    messages,
    rooms,
    currentRoom,
    loading,
    error,
    isConnected,
    sendMessage,
    sendFileMessage,
    uploadFile,
    createRoom,
    joinRoom,
    loadMessages,
    loadRooms,
    clearError: () => setError(null)
  }
}
