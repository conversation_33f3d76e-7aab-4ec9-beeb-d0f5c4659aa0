fix: correct monorepo tsconfig and package configurations

This commit resolves persistent "Module not found" errors by addressing fundamental configuration issues in the monorepo setup:

1.  **Added tsconfig.json to Packages:** Created `tsconfig.json` files for the `ui` and `database` packages. This provides the necessary context for TypeScript to resolve internal paths and is the primary fix for the module resolution failures.

2.  **Enabled Package Transpilation:** Updated all `next.config.mjs` files across the applications to include `transpilePackages`. This ensures that the TypeScript code from the shared packages is correctly compiled as part of the application build process.

3.  **Standardized Package Exports:** Simplified the `exports` map in `packages/ui/package.json` to follow best practices, reducing complexity.

4.  **Fixed Auth App 404:** Added a root page to the `auth` app to redirect users to the login page, resolving a 404 error.

These changes create a correctly configured TypeScript monorepo where applications can reliably consume shared packages.