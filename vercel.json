{"version": 2, "monorepo": true, "buildCommand": "cd ../.. && npx turbo run build --filter=$VERCEL_GIT_REPO_SLUG", "installCommand": "npm install", "framework": "nextjs", "builds": [{"src": "apps/landing/package.json", "use": "@vercel/next"}, {"src": "apps/auth/package.json", "use": "@vercel/next"}, {"src": "apps/dashboard/package.json", "use": "@vercel/next"}, {"src": "apps/admin/package.json", "use": "@vercel/next"}], "crons": [{"path": "/api/cron/bot-detection", "schedule": "*/5 * * * *"}, {"path": "/api/cron/process-payouts", "schedule": "0 0 * * *"}, {"path": "/api/cron/update-view-counts", "schedule": "*/10 * * * *"}]}