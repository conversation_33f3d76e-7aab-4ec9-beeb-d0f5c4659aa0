
import { createSupabaseServerClient } from "@creatorboost/database"
import { redirect } from "next/navigation"
import { cookies } from "next/headers"
import Link from "next/link"
import { Icons } from "@creatorboost/ui/icons";
// import { ThemeProvider } from "@creatorboost/ui/theme-provider";
import { UserNav } from "./components/user-nav";

interface AdminLayoutProps {
  children: React.ReactNode
}

async function checkAdminRole(supabase: any) {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return false;

    const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .single();
    
    return profile?.role === 'admin';
}

export default async function AdminLayout({ children }: AdminLayoutProps) {
  const cookieStore = await cookies()
  const supabase = createSupabaseServerClient(cookieStore)
  
  const isAdmin = await checkAdminRole(supabase);

  if (!isAdmin) {
    redirect(process.env.NEXT_PUBLIC_AUTH_URL || "/login")
  }

  return (
      <div className="flex min-h-screen w-full flex-col">
        <header className="sticky top-0 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6">
          <nav className="hidden flex-col gap-6 text-lg font-medium md:flex md:flex-row md:items-center md:gap-5 md:text-sm lg:gap-6">
            <Link
              href="/admin"
              className="flex items-center gap-2 text-lg font-semibold md:text-base"
            >
              <Icons.logo className="h-6 w-6" />
              <span className="sr-only">Admin Panel</span>
            </Link>
            <Link
              href="/admin"
              className="text-foreground transition-colors hover:text-foreground"
            >
              Dashboard
            </Link>
            <Link
              href="/admin/users"
              className="text-muted-foreground transition-colors hover:text-foreground"
            >
              Users
            </Link>
            <Link
              href="/admin/campaigns"
              className="text-muted-foreground transition-colors hover:text-foreground"
            >
              Campaigns
            </Link>
          </nav>
          <div className="flex w-full items-center gap-4 md:ml-auto md:gap-2 lg:gap-4">
            <div className="ml-auto flex-1 sm:flex-initial">
              <UserNav />
            </div>
          </div>
        </header>
        <main className="flex flex-1 flex-col gap-4 p-4 md:gap-8 md:p-8">
          {children}
        </main>
      </div>
  )
}
