"use client"

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseBrowserClient } from '@creatorboost/database'
import { useAuth } from './use-auth'

export interface AppNotification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error' | 'campaign' | 'chat' | 'payment'
  read: boolean
  created_at: string
  action_url?: string
  action_text?: string
  user_id: string
  campaign_id?: string
  metadata?: Record<string, any>
}

export function useNotifications() {
  const { user } = useAuth()
  const [notifications, setNotifications] = useState<AppNotification[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [unreadCount, setUnreadCount] = useState(0)

  const supabase = createSupabaseBrowserClient()

  // Load notifications
  const loadNotifications = useCallback(async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      const { data, error: fetchError } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(50)

      if (fetchError) throw fetchError

      setNotifications(data || [])
      setUnreadCount((data || []).filter(n => !n.read).length)
    } catch (err) {
      console.error('Error loading notifications:', err)
      setError(err instanceof Error ? err.message : 'Failed to load notifications')
    } finally {
      setLoading(false)
    }
  }, [user, supabase])

  // Mark notification as read
  const markAsRead = useCallback(async (notificationId: string) => {
    if (!user) return

    try {
      const { error: updateError } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('id', notificationId)
        .eq('user_id', user.id)

      if (updateError) throw updateError

      setNotifications(prev => 
        prev.map(n => 
          n.id === notificationId ? { ...n, read: true } : n
        )
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    } catch (err) {
      console.error('Error marking notification as read:', err)
      setError(err instanceof Error ? err.message : 'Failed to mark as read')
    }
  }, [user, supabase])

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    if (!user) return

    try {
      const { error: updateError } = await supabase
        .from('notifications')
        .update({ read: true })
        .eq('user_id', user.id)
        .eq('read', false)

      if (updateError) throw updateError

      setNotifications(prev => 
        prev.map(n => ({ ...n, read: true }))
      )
      setUnreadCount(0)
    } catch (err) {
      console.error('Error marking all notifications as read:', err)
      setError(err instanceof Error ? err.message : 'Failed to mark all as read')
    }
  }, [user, supabase])

  // Create notification (for system use)
  const createNotification = useCallback(async (
    notification: Omit<Notification, 'id' | 'created_at' | 'read'>
  ) => {
    try {
      const { data, error: insertError } = await supabase
        .from('notifications')
        .insert({
          ...notification,
          read: false,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (insertError) throw insertError

      return data
    } catch (err) {
      console.error('Error creating notification:', err)
      throw err
    }
  }, [supabase])

  // Send notification to specific user
  const sendNotification = useCallback(async (
    userId: string,
    title: string,
    message: string,
    type: AppNotification['type'] = 'info',
    options?: {
      action_url?: string
      action_text?: string
      campaign_id?: string
      metadata?: Record<string, any>
    }
  ) => {
    try {
      return await createNotification({
        user_id: userId,
        title,
        message,
        type,
        ...options
      })
    } catch (err) {
      console.error('Error sending notification:', err)
      throw err
    }
  }, [createNotification])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Set up real-time subscription
  useEffect(() => {
    if (!user) return

    loadNotifications()

    // Subscribe to new notifications
    const subscription = supabase
      .channel(`notifications-${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          const newNotification = payload.new as Notification
          setNotifications(prev => [newNotification, ...prev])
          setUnreadCount(prev => prev + 1)
        }
      )
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${user.id}`
        },
        (payload) => {
          const updatedNotification = payload.new as Notification
          setNotifications(prev => 
            prev.map(n => 
              n.id === updatedNotification.id ? updatedNotification : n
            )
          )
          
          // Update unread count
          if (updatedNotification.read && !payload.old?.read) {
            setUnreadCount(prev => Math.max(0, prev - 1))
          } else if (!updatedNotification.read && payload.old?.read) {
            setUnreadCount(prev => prev + 1)
          }
        }
      )
      .subscribe()

    return () => {
      subscription.unsubscribe()
    }
  }, [user, loadNotifications, supabase])

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    sendNotification,
    createNotification,
    clearError,
    refresh: loadNotifications
  }
}
