"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { But<PERSON> } from "@creatorboost/ui/button"
import { Input } from "@creatorboost/ui/input"
import { Textarea } from "@creatorboost/ui/textarea"
// import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter } from "@creatorboost/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@creatorboost/ui/card"
import { Badge } from "@creatorboost/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@creatorboost/ui/table"
import { createSupabaseBrowserClient } from "@creatorboost/database"
import { useToast } from "@creatorboost/ui/use-toast"
import { useAuth, useSocialMedia } from "@creatorboost/hooks"
import { Icons } from "@creatorboost/ui/icons"

const campaignSchema = z.object({
  name: z.string().min(5, "Campaign name must be at least 5 characters"),
  description: z.string().optional(),
  budget: z.coerce.number().positive("Budget must be a positive number"),
  cost_per_view: z.coerce.number().positive("Cost per view must be a positive number"),
  materials_url: z.string().url("Please enter a valid URL"),
})

type CampaignForm = z.infer<typeof campaignSchema>

export default function CreatorDashboard() {
  const [open, setOpen] = useState(false)
  const { toast } = useToast()
  const { user } = useAuth()
  const { socialAccounts, loading: socialLoading, connectSocialAccount } = useSocialMedia()
  const supabase = createSupabaseBrowserClient()
  const { register, handleSubmit, formState: { errors }, reset } = useForm<CampaignForm>({
    resolver: zodResolver(campaignSchema),
  })

  // Mock data for demonstration
  const campaigns = [
    {
      id: 1,
      name: "Summer Product Launch",
      budget: 5000,
      spent: 2500,
      status: "active",
      submissions: 12,
      views: 45000,
      cost_per_view: 0.1
    },
    {
      id: 2,
      name: "Brand Awareness Campaign",
      budget: 3000,
      spent: 3000,
      status: "completed",
      submissions: 8,
      views: 30000,
      cost_per_view: 0.1
    }
  ]

  const onSubmit = async (data: CampaignForm) => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) throw new Error("User not authenticated")

      const { error } = await supabase.from("campaigns").insert({
        ...data,
        creator_id: user.id,
      })

      if (error) throw error

      toast({ title: "Success", description: "Campaign created successfully!" })
      reset()
      setOpen(false)
    } catch (error: any) {
      toast({ title: "Error", description: error.message, variant: "destructive" })
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold tracking-tight">Creator Dashboard</h1>
        <Button onClick={() => setOpen(true)}>Create New Campaign</Button>

        {open && (
          <div className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm">
            <div className="fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg sm:rounded-lg">
              <div className="flex flex-col space-y-1.5 text-center sm:text-left">
                <h2 className="text-lg font-semibold leading-none tracking-tight">Create New Campaign</h2>
              </div>
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
                <div>
                  <Input {...register("name")} placeholder="Campaign Name" />
                  {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name.message}</p>}
                </div>
                <Textarea {...register("description")} placeholder="Campaign Description (optional)" />
                <div>
                  <Input {...register("budget")} type="number" placeholder="Total Budget (e.g., 1000)" />
                  {errors.budget && <p className="text-sm text-red-500 mt-1">{errors.budget.message}</p>}
                </div>
                <div>
                  <Input {...register("cost_per_view")} type="number" step="0.001" placeholder="Cost Per View (e.g., 0.1)" />
                  {errors.cost_per_view && <p className="text-sm text-red-500 mt-1">{errors.cost_per_view.message}</p>}
                </div>
                <div>
                  <Input {...register("materials_url")} placeholder="URL to creative materials (Google Drive, etc.)" />
                  {errors.materials_url && <p className="text-sm text-red-500 mt-1">{errors.materials_url.message}</p>}
                </div>
                <div className="flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2">
                  <Button type="button" variant="outline" onClick={() => setOpen(false)}>Cancel</Button>
                  <Button type="submit">Create Campaign</Button>
                </div>
              </form>
            </div>
          </div>
        )}
      </div>

      {/* Campaign Stats */}
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Campaigns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${campaigns.reduce((acc, c) => acc + c.budget, 0).toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Views</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.reduce((acc, c) => acc + c.views, 0).toLocaleString()}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Campaigns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{campaigns.filter(c => c.status === 'active').length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Social Media Accounts */}
      <Card>
        <CardHeader>
          <CardTitle>Connected Social Media Accounts</CardTitle>
          <p className="text-sm text-muted-foreground">
            Connect your social media accounts to track engagement and ensure accurate payout calculations.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {/* TikTok */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Icons.tiktok className="h-8 w-8" />
                <div>
                  <p className="font-medium">TikTok</p>
                  {socialAccounts.find(acc => acc.platform === 'tiktok') ? (
                    <p className="text-sm text-muted-foreground">
                      @{socialAccounts.find(acc => acc.platform === 'tiktok')?.username}
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground">Not connected</p>
                  )}
                </div>
              </div>
              {socialAccounts.find(acc => acc.platform === 'tiktok') ? (
                <Badge variant="secondary">Connected</Badge>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => connectSocialAccount('tiktok')}
                >
                  Connect
                </Button>
              )}
            </div>

            {/* Instagram */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Icons.instagram className="h-8 w-8" />
                <div>
                  <p className="font-medium">Instagram</p>
                  {socialAccounts.find(acc => acc.platform === 'instagram') ? (
                    <p className="text-sm text-muted-foreground">
                      @{socialAccounts.find(acc => acc.platform === 'instagram')?.username}
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground">Not connected</p>
                  )}
                </div>
              </div>
              {socialAccounts.find(acc => acc.platform === 'instagram') ? (
                <Badge variant="secondary">Connected</Badge>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => connectSocialAccount('instagram')}
                >
                  Connect
                </Button>
              )}
            </div>

            {/* YouTube */}
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Icons.youtube className="h-8 w-8" />
                <div>
                  <p className="font-medium">YouTube</p>
                  {socialAccounts.find(acc => acc.platform === 'youtube') ? (
                    <p className="text-sm text-muted-foreground">
                      @{socialAccounts.find(acc => acc.platform === 'youtube')?.username}
                    </p>
                  ) : (
                    <p className="text-sm text-muted-foreground">Not connected</p>
                  )}
                </div>
              </div>
              {socialAccounts.find(acc => acc.platform === 'youtube') ? (
                <Badge variant="secondary">Connected</Badge>
              ) : (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => connectSocialAccount('youtube')}
                >
                  Connect
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Campaigns Table */}
      <Card>
        <CardHeader>
          <CardTitle>My Campaigns</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Campaign Name</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Spent</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submissions</TableHead>
                <TableHead>Views</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {campaigns.map((campaign) => (
                <TableRow key={campaign.id}>
                  <TableCell className="font-medium">{campaign.name}</TableCell>
                  <TableCell>${campaign.budget.toLocaleString()}</TableCell>
                  <TableCell>${campaign.spent.toLocaleString()}</TableCell>
                  <TableCell>
                    <Badge variant={campaign.status === 'active' ? 'default' : 'secondary'}>
                      {campaign.status}
                    </Badge>
                  </TableCell>
                  <TableCell>{campaign.submissions}</TableCell>
                  <TableCell>{campaign.views.toLocaleString()}</TableCell>
                  <TableCell>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">View</Button>
                      <Button variant="outline" size="sm">Edit</Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
