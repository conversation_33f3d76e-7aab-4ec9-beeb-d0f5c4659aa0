import type { SocialMediaMetrics, FraudDetectionResult, PayoutCalculation } from '../types'
import { detectSocialMediaFraud, calculateFairPayout } from './bot-detection-engine'

export interface AnalysisRequest {
  submissionId: string
  socialMediaMetrics: SocialMediaMetrics
  basePayoutAmount: number
  campaignId: string
  userId: string
}

export interface AnalysisResult {
  submissionId: string
  fraudDetection: FraudDetectionResult
  payoutCalculation: PayoutCalculation
  status: 'approved' | 'delayed' | 'review_required' | 'rejected'
  nextAction: string
  reviewDeadline?: string
}

/**
 * Main analyzer class that orchestrates fraud detection and payout calculation
 */
export class PromotionAnalyzer {
  
  /**
   * Analyze a promotion submission for fraud and calculate fair payout
   */
  static async analyzeSubmission(request: AnalysisRequest): Promise<AnalysisResult> {
    // Step 1: Run fraud detection
    const fraudDetection = detectSocialMediaFraud(request.socialMediaMetrics)
    
    // Step 2: Calculate platform multiplier based on platform and metrics
    const platformMultiplier = this.getPlatformMultiplier(
      request.socialMediaMetrics.platform,
      request.socialMediaMetrics.views,
      request.socialMediaMetrics.accountInfo.followerCount
    )
    
    // Step 3: Calculate fair payout
    const payoutCalculation = calculateFairPayout(
      request.basePayoutAmount,
      fraudDetection,
      platformMultiplier
    )
    
    // Step 4: Determine status and next action
    const { status, nextAction, reviewDeadline } = this.determineStatus(fraudDetection)
    
    return {
      submissionId: request.submissionId,
      fraudDetection,
      payoutCalculation,
      status,
      nextAction,
      reviewDeadline
    }
  }

  /**
   * Batch analyze multiple submissions
   */
  static async batchAnalyze(requests: AnalysisRequest[]): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = []
    
    for (const request of requests) {
      try {
        const result = await this.analyzeSubmission(request)
        results.push(result)
      } catch (error) {
        console.error(`Failed to analyze submission ${request.submissionId}:`, error)
        // Add error result
        results.push({
          submissionId: request.submissionId,
          fraudDetection: {
            fraudScore: 100,
            fraudLevel: 'fraud',
            flags: ['analysis_error'],
            payoutEligible: false,
            payoutMultiplier: 0,
            payoutDelay: -1,
            recommendation: 'Analysis failed - manual review required',
            details: {
              engagement: { score: 0, flags: [] },
              ratios: { score: 0, flags: [] },
              account: { score: 0, flags: [] },
              temporal: { score: 0, flags: [] },
              consistency: { score: 0, flags: [] }
            }
          },
          payoutCalculation: {
            originalAmount: request.basePayoutAmount,
            adjustedAmount: request.basePayoutAmount,
            deductions: [{ reason: 'Analysis error', amount: request.basePayoutAmount }],
            finalAmount: 0
          },
          status: 'review_required',
          nextAction: 'Manual review required due to analysis error'
        })
      }
    }
    
    return results
  }

  /**
   * Get platform-specific multiplier based on performance metrics
   */
  private static getPlatformMultiplier(
    platform: string,
    views: number,
    followerCount: number
  ): number {
    const baseMultipliers = {
      tiktok: 1.0,
      instagram: 1.2,
      youtube: 1.5
    }

    let multiplier = baseMultipliers[platform as keyof typeof baseMultipliers] || 1.0

    // Bonus for high-performing content
    if (views > followerCount * 2) {
      multiplier += 0.1 // 10% bonus for viral content
    }

    // Bonus for large accounts
    if (followerCount > 100000) {
      multiplier += 0.05 // 5% bonus for large accounts
    } else if (followerCount > 10000) {
      multiplier += 0.02 // 2% bonus for medium accounts
    }

    return Math.min(multiplier, 2.0) // Cap at 2x multiplier
  }

  /**
   * Determine submission status and next action based on fraud detection
   */
  private static determineStatus(fraudDetection: FraudDetectionResult): {
    status: 'approved' | 'delayed' | 'review_required' | 'rejected'
    nextAction: string
    reviewDeadline?: string
  } {
    const now = new Date()
    
    switch (fraudDetection.fraudLevel) {
      case 'safe':
        return {
          status: 'approved',
          nextAction: 'Process payout immediately'
        }
        
      case 'warning':
        const delayedDate = new Date(now.getTime() + fraudDetection.payoutDelay * 60 * 60 * 1000)
        return {
          status: 'delayed',
          nextAction: `Process payout after ${fraudDetection.payoutDelay} hours`,
          reviewDeadline: delayedDate.toISOString()
        }
        
      case 'suspicious':
        const reviewDate = new Date(now.getTime() + fraudDetection.payoutDelay * 60 * 60 * 1000)
        return {
          status: 'review_required',
          nextAction: 'Manual review required - suspicious activity detected',
          reviewDeadline: reviewDate.toISOString()
        }
        
      case 'fraud':
        return {
          status: 'rejected',
          nextAction: 'Reject payout and consider account suspension'
        }
        
      default:
        return {
          status: 'review_required',
          nextAction: 'Manual review required - unknown fraud level'
        }
    }
  }

  /**
   * Generate detailed report for manual review
   */
  static generateReviewReport(result: AnalysisResult): string {
    const { fraudDetection, payoutCalculation } = result
    
    let report = `FRAUD DETECTION REPORT\n`
    report += `========================\n\n`
    report += `Submission ID: ${result.submissionId}\n`
    report += `Fraud Score: ${fraudDetection.fraudScore}/100\n`
    report += `Fraud Level: ${fraudDetection.fraudLevel.toUpperCase()}\n`
    report += `Status: ${result.status.toUpperCase()}\n\n`
    
    report += `PAYOUT CALCULATION:\n`
    report += `Original Amount: $${payoutCalculation.originalAmount.toFixed(2)}\n`
    report += `Adjusted Amount: $${payoutCalculation.adjustedAmount.toFixed(2)}\n`
    report += `Final Amount: $${payoutCalculation.finalAmount.toFixed(2)}\n\n`
    
    if (payoutCalculation.deductions.length > 0) {
      report += `DEDUCTIONS:\n`
      payoutCalculation.deductions.forEach(deduction => {
        report += `- ${deduction.reason}: -$${deduction.amount.toFixed(2)}\n`
      })
      report += `\n`
    }
    
    if (fraudDetection.flags.length > 0) {
      report += `FRAUD FLAGS:\n`
      fraudDetection.flags.forEach(flag => {
        report += `- ${flag.replace(/_/g, ' ')}\n`
      })
      report += `\n`
    }
    
    report += `RECOMMENDATION:\n`
    report += `${fraudDetection.recommendation}\n\n`
    
    report += `NEXT ACTION:\n`
    report += `${result.nextAction}\n`
    
    if (result.reviewDeadline) {
      report += `\nREVIEW DEADLINE:\n`
      report += `${new Date(result.reviewDeadline).toLocaleString()}\n`
    }
    
    return report
  }

  /**
   * Check if submission needs immediate attention
   */
  static needsImmediateAttention(result: AnalysisResult): boolean {
    return (
      result.fraudDetection.fraudLevel === 'fraud' ||
      result.fraudDetection.fraudScore >= 90 ||
      result.fraudDetection.flags.includes('perfect_engagement_rate') ||
      result.fraudDetection.flags.includes('too_many_likes_for_views')
    )
  }
}
