import { NextRequest, NextResponse } from 'next/server'

interface InstagramMediaResponse {
  id: string
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM'
  media_url: string
  permalink: string
  caption: string
  timestamp: string
  like_count?: number
  comments_count?: number
  username: string
}

interface InstagramInsightsResponse {
  data: Array<{
    name: string
    values: Array<{
      value: number
    }>
  }>
}

export async function POST(request: NextRequest) {
  try {
    const { mediaId } = await request.json()
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const accessToken = authHeader.substring(7)

    if (!mediaId) {
      return NextResponse.json(
        { error: 'Media ID is required' },
        { status: 400 }
      )
    }

    // Fetch basic media information
    const mediaResponse = await fetch(
      `https://graph.instagram.com/${mediaId}?fields=id,media_type,media_url,permalink,caption,timestamp,like_count,comments_count,username&access_token=${accessToken}`
    )

    if (!mediaResponse.ok) {
      const errorData = await mediaResponse.text()
      console.error('Instagram Media API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch media data from Instagram' },
        { status: mediaResponse.status }
      )
    }

    const mediaData: InstagramMediaResponse = await mediaResponse.json()

    // For video content, try to get insights (reach, impressions)
    let insights = null
    if (mediaData.media_type === 'VIDEO') {
      try {
        const insightsResponse = await fetch(
          `https://graph.instagram.com/${mediaId}/insights?metric=reach,impressions,video_views&access_token=${accessToken}`
        )

        if (insightsResponse.ok) {
          insights = await insightsResponse.json()
        }
      } catch (error) {
        console.warn('Failed to fetch Instagram insights:', error)
        // Continue without insights
      }
    }

    // Extract video views from insights if available
    let videoViews = 0
    if (insights?.data) {
      const videoViewsMetric = insights.data.find((metric: any) => metric.name === 'video_views')
      if (videoViewsMetric?.values?.[0]?.value) {
        videoViews = videoViewsMetric.values[0].value
      }
    }

    // Return standardized media data
    return NextResponse.json({
      id: mediaData.id,
      media_type: mediaData.media_type,
      media_url: mediaData.media_url,
      permalink: mediaData.permalink,
      caption: mediaData.caption,
      timestamp: mediaData.timestamp,
      like_count: mediaData.like_count || 0,
      comments_count: mediaData.comments_count || 0,
      video_views: videoViews,
      username: mediaData.username
    })

  } catch (error) {
    console.error('Instagram API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get user's recent media
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id') || 'me'
    const limit = searchParams.get('limit') || '25'
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const accessToken = authHeader.substring(7)

    // Fetch user's recent media
    const response = await fetch(
      `https://graph.instagram.com/${userId}/media?fields=id,media_type,media_url,permalink,caption,timestamp,like_count,comments_count&limit=${limit}&access_token=${accessToken}`
    )

    if (!response.ok) {
      const errorData = await response.text()
      console.error('Instagram Media List API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch media list from Instagram' },
        { status: response.status }
      )
    }

    const data = await response.json()

    return NextResponse.json(data)

  } catch (error) {
    console.error('Instagram Media List API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get Instagram account information
export async function PATCH(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { error: 'Missing or invalid authorization header' },
        { status: 401 }
      )
    }

    const accessToken = authHeader.substring(7)

    // Fetch account information
    const response = await fetch(
      `https://graph.instagram.com/me?fields=id,username,account_type,media_count,followers_count&access_token=${accessToken}`
    )

    if (!response.ok) {
      const errorData = await response.text()
      console.error('Instagram Account API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch account data from Instagram' },
        { status: response.status }
      )
    }

    const accountData = await response.json()

    return NextResponse.json({
      id: accountData.id,
      username: accountData.username,
      account_type: accountData.account_type,
      media_count: accountData.media_count,
      followers_count: accountData.followers_count || 0
    })

  } catch (error) {
    console.error('Instagram Account API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
