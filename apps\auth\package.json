{"name": "@creatorboost/auth", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@creatorboost/database": "*", "@creatorboost/ui": "*", "@hookform/resolvers": "^3.3.2", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.39.0", "next": "^15.3.4", "react-hook-form": "^7.48.2", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "eslint": "^8.55.0", "eslint-config-next": "^15.3.4", "typescript": "^5.3.2"}}