"use client"

import { useState, useCallback } from 'react'
import { createClient } from '@supabase/supabase-js'
import { PromotionAnalyzer, type AnalysisRequest, type AnalysisResult } from '@creatorboost/bot-detection/lib/analyzer'
import { useSocialAPI } from './use-social-api'
import { useAuth } from './use-auth'

interface FraudDetectionState {
  loading: boolean
  error: string | null
  results: AnalysisResult[]
  currentAnalysis: AnalysisResult | null
}

interface SubmissionData {
  id: string
  campaignId: string
  postUrl: string
  platform: 'tiktok' | 'instagram' | 'youtube'
  basePayoutAmount: number
  socialAccountId: string
}

export function useFraudDetection() {
  const { user } = useAuth()
  const { fetchSocialMediaData } = useSocialAPI()
  const [state, setState] = useState<FraudDetectionState>({
    loading: false,
    error: null,
    results: [],
    currentAnalysis: null
  })

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )

  /**
   * Analyze a single submission for fraud
   */
  const analyzeSubmission = useCallback(async (submissionData: SubmissionData) => {
    if (!user) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }))
      return null
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      // 1. Get social account info and access token
      const { data: socialAccount, error: accountError } = await supabase
        .from('social_media_accounts')
        .select('*')
        .eq('id', submissionData.socialAccountId)
        .eq('user_id', user.id)
        .single()

      if (accountError || !socialAccount) {
        throw new Error('Social media account not found')
      }

      // 2. Fetch real-time social media metrics
      const socialMetrics = await fetchSocialMediaData(
        submissionData.platform,
        socialAccount.access_token,
        submissionData.postUrl
      )

      if (!socialMetrics) {
        throw new Error('Failed to fetch social media metrics')
      }

      // 3. Enhance metrics with account info
      const enhancedMetrics = {
        ...socialMetrics,
        accountInfo: {
          followerCount: socialAccount.follower_count || 0,
          accountAge: calculateAccountAge(socialAccount.created_at),
          verificationStatus: socialAccount.is_verified || false
        }
      }

      // 4. Run fraud analysis
      const analysisRequest: AnalysisRequest = {
        submissionId: submissionData.id,
        socialMediaMetrics: enhancedMetrics,
        basePayoutAmount: submissionData.basePayoutAmount,
        campaignId: submissionData.campaignId,
        userId: user.id
      }

      const result = await PromotionAnalyzer.analyzeSubmission(analysisRequest)

      // 5. Save analysis result to database
      await saveAnalysisResult(result, enhancedMetrics)

      setState(prev => ({
        ...prev,
        loading: false,
        currentAnalysis: result,
        results: [result, ...prev.results.filter(r => r.submissionId !== result.submissionId)]
      }))

      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Analysis failed'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [user, fetchSocialMediaData, supabase])

  /**
   * Batch analyze multiple submissions
   */
  const batchAnalyze = useCallback(async (submissions: SubmissionData[]) => {
    if (!user) {
      setState(prev => ({ ...prev, error: 'User not authenticated' }))
      return []
    }

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const analysisRequests: AnalysisRequest[] = []

      // Prepare all analysis requests
      for (const submission of submissions) {
        const { data: socialAccount } = await supabase
          .from('social_media_accounts')
          .select('*')
          .eq('id', submission.socialAccountId)
          .eq('user_id', user.id)
          .single()

        if (socialAccount) {
          const socialMetrics = await fetchSocialMediaData(
            submission.platform,
            socialAccount.access_token,
            submission.postUrl
          )

          if (socialMetrics) {
            const enhancedMetrics = {
              ...socialMetrics,
              accountInfo: {
                followerCount: socialAccount.follower_count || 0,
                accountAge: calculateAccountAge(socialAccount.created_at),
                verificationStatus: socialAccount.is_verified || false
              }
            }

            analysisRequests.push({
              submissionId: submission.id,
              socialMediaMetrics: enhancedMetrics,
              basePayoutAmount: submission.basePayoutAmount,
              campaignId: submission.campaignId,
              userId: user.id
            })
          }
        }
      }

      // Run batch analysis
      const results = await PromotionAnalyzer.batchAnalyze(analysisRequests)

      // Save all results
      for (const result of results) {
        const request = analysisRequests.find(r => r.submissionId === result.submissionId)
        if (request) {
          await saveAnalysisResult(result, request.socialMediaMetrics)
        }
      }

      setState(prev => ({
        ...prev,
        loading: false,
        results: [...results, ...prev.results.filter(r => !results.find(nr => nr.submissionId === r.submissionId))]
      }))

      return results

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Batch analysis failed'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
      return []
    }
  }, [user, fetchSocialMediaData, supabase])

  /**
   * Get analysis history for a user
   */
  const getAnalysisHistory = useCallback(async (limit: number = 50) => {
    if (!user) return

    setState(prev => ({ ...prev, loading: true, error: null }))

    try {
      const { data, error } = await supabase
        .from('fraud_analysis_results')
        .select(`
          *,
          submissions (
            id,
            post_url,
            platform,
            campaigns (
              title,
              brand_name
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) throw error

      const results = data?.map(item => ({
        submissionId: item.submission_id,
        fraudDetection: item.fraud_detection,
        payoutCalculation: item.payout_calculation,
        status: item.status,
        nextAction: item.next_action,
        reviewDeadline: item.review_deadline
      })) || []

      setState(prev => ({ ...prev, loading: false, results }))

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch analysis history'
      setState(prev => ({ ...prev, loading: false, error: errorMessage }))
    }
  }, [user, supabase])

  /**
   * Save analysis result to database
   */
  const saveAnalysisResult = async (result: AnalysisResult, metrics: any) => {
    await supabase
      .from('fraud_analysis_results')
      .upsert({
        submission_id: result.submissionId,
        user_id: user?.id,
        fraud_detection: result.fraudDetection,
        payout_calculation: result.payoutCalculation,
        status: result.status,
        next_action: result.nextAction,
        review_deadline: result.reviewDeadline,
        social_metrics: metrics,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'submission_id'
      })
  }

  /**
   * Clear error state
   */
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }))
  }, [])

  /**
   * Generate review report
   */
  const generateReport = useCallback((result: AnalysisResult) => {
    return PromotionAnalyzer.generateReviewReport(result)
  }, [])

  return {
    ...state,
    analyzeSubmission,
    batchAnalyze,
    getAnalysisHistory,
    clearError,
    generateReport,
    needsAttention: (result: AnalysisResult) => PromotionAnalyzer.needsImmediateAttention(result)
  }
}

/**
 * Calculate account age in days
 */
function calculateAccountAge(createdAt: string): number {
  const created = new Date(createdAt)
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - created.getTime())
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
}
