export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          role: "creator" | "promoter" | "admin"
          profile: any
          wallet_balance: number
          created_at: string
        }
        Insert: {
          id?: string
          email: string
          role: "creator" | "promoter" | "admin"
          profile?: any
          wallet_balance?: number
          created_at?: string
        }
        Update: {
          id?: string
          email?: string
          role?: "creator" | "promoter" | "admin"
          profile?: any
          wallet_balance?: number
          created_at?: string
        }
      }
      campaigns: {
        Row: {
          id: string
          creator_id: string
          title: string
          description: string
          budget_total: number
          pay_per_view: number
          requirements: string[]
          status: "active" | "paused" | "completed"
          content_files: any
          created_at: string
        }
        Insert: {
          id?: string
          creator_id: string
          title: string
          description: string
          budget_total: number
          pay_per_view: number
          requirements?: string[]
          status?: "active" | "paused" | "completed"
          content_files?: any
          created_at?: string
        }
        Update: {
          id?: string
          creator_id?: string
          title?: string
          description?: string
          budget_total?: number
          pay_per_view?: number
          requirements?: string[]
          status?: "active" | "paused" | "completed"
          content_files?: any
          created_at?: string
        }
      }
      promotions: {
        Row: {
          id: string
          campaign_id: string
          promoter_id: string
          social_platform: "tiktok" | "instagram"
          post_url: string
          views_count: number
          earnings: number
          bot_score: number
          status: "active" | "under_review" | "approved" | "rejected"
          created_at: string
        }
        Insert: {
          id?: string
          campaign_id: string
          promoter_id: string
          social_platform: "tiktok" | "instagram"
          post_url: string
          views_count?: number
          earnings?: number
          bot_score?: number
          status?: "active" | "under_review" | "approved" | "rejected"
          created_at?: string
        }
        Update: {
          id?: string
          campaign_id?: string
          promoter_id?: string
          social_platform?: "tiktok" | "instagram"
          post_url?: string
          views_count?: number
          earnings?: number
          bot_score?: number
          status?: "active" | "under_review" | "approved" | "rejected"
          created_at?: string
        }
      }
      view_logs: {
        Row: {
          id: string
          promotion_id: string
          timestamp: string
          view_count: number
          likes_count: number
          comments_count: number
          bot_indicators: any
        }
        Insert: {
          id?: string
          promotion_id: string
          timestamp?: string
          view_count: number
          likes_count: number
          comments_count: number
          bot_indicators?: any
        }
        Update: {
          id?: string
          promotion_id?: string
          timestamp?: string
          view_count?: number
          likes_count?: number
          comments_count?: number
          bot_indicators?: any
        }
      }
      payouts: {
        Row: {
          id: string
          user_id: string
          amount: number
          status: "pending" | "processing" | "completed" | "failed"
          processed_at: string | null
        }
        Insert: {
          id?: string
          user_id: string
          amount: number
          status?: "pending" | "processing" | "completed" | "failed"
          processed_at?: string | null
        }
        Update: {
          id?: string
          user_id?: string
          amount?: number
          status?: "pending" | "processing" | "completed" | "failed"
          processed_at?: string | null
        }
      }
    }
  }
}
