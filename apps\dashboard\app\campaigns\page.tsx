
import { createSupabaseServerClient } from "@creatorboost/database";
import { cookies } from "next/headers";
// import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow, Badge, Button } from "@creatorboost/ui";
import Link from "next/link";

async function getCampaigns() {
  const cookieStore = await cookies();
  const supabase = createSupabaseServerClient(cookieStore);
  const { data: campaigns, error } = await supabase.from("campaigns").select(`
    *,
    profiles (
      full_name
    )
  `);

  if (error) {
    console.error("Error fetching campaigns:", error);
    return [];
  }

  return campaigns;
}

export default async function CampaignsPage() {
  const campaigns = await getCampaigns();

  return (
    <>
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-3xl font-bold tracking-tight">Campaigns</h2>
        <div className="flex items-center space-x-2">
          <button className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">Create Campaign</button>
        </div>
      </div>
      <div className="rounded-md border p-4">
        <div>
          <div className="grid grid-cols-6 gap-4 font-semibold border-b pb-2 mb-4">
            <div>Campaign Name</div>
            <div>Creator</div>
            <div>Status</div>
            <div>Budget</div>
            <div>Rate per View</div>
            <div>Actions</div>
          </div>
          {campaigns.map((campaign) => (
            <div key={campaign.id} className="grid grid-cols-6 gap-4 py-2 border-b">
              <div className="font-medium">{campaign.name}</div>
              <div>{campaign.profiles?.full_name || 'N/A'}</div>
              <div>
                <span className={`px-2 py-1 rounded text-xs ${campaign.status === 'active' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'}`}>
                  {campaign.status}
                </span>
              </div>
              <div>${campaign.total_budget}</div>
              <div>${campaign.rate_per_view}</div>
              <div>
                <Link href={`/campaigns/${campaign.id}`}>
                  <button className="px-3 py-1 border rounded text-sm hover:bg-gray-50">
                    View Details
                  </button>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}
