"use client"

import { useState, useCallback } from 'react'

// Platform-specific API interfaces
interface TikTokVideoData {
  id: string
  view_count: number
  like_count: number
  comment_count: number
  share_count: number
  create_time: number
  video_description: string
  username: string
  display_name: string
}

interface InstagramMediaData {
  id: string
  media_type: 'IMAGE' | 'VIDEO' | 'CAROUSEL_ALBUM'
  media_url: string
  permalink: string
  caption: string
  timestamp: string
  like_count?: number
  comments_count?: number
  username: string
}

interface YouTubeVideoData {
  id: string
  snippet: {
    title: string
    description: string
    publishedAt: string
    channelTitle: string
    channelId: string
  }
  statistics: {
    viewCount: string
    likeCount: string
    commentCount: string
    favoriteCount: string
  }
}

interface SocialMediaMetrics {
  platform: 'tiktok' | 'instagram' | 'youtube'
  postId: string
  postUrl: string
  views: number
  likes: number
  comments: number
  shares: number
  engagementRate: number
  timestamp: string
  username: string
  accountInfo: {
    followerCount: number
    accountAge: number
    verificationStatus: boolean
  }
}

export function useSocialAPI() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // TikTok API integration
  const fetchTikTokData = useCallback(async (
    accessToken: string,
    videoUrl: string
  ): Promise<SocialMediaMetrics | null> => {
    try {
      setLoading(true)
      setError(null)

      // Extract video ID from URL
      const videoId = extractTikTokVideoId(videoUrl)
      if (!videoId) {
        throw new Error('Invalid TikTok video URL')
      }

      // Fetch video data from TikTok API
      const response = await fetch('/api/social/tiktok/video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ videoId })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch TikTok data')
      }

      const data: TikTokVideoData = await response.json()

      // Calculate engagement rate
      const totalEngagement = data.like_count + data.comment_count + data.share_count
      const engagementRate = data.view_count > 0 ? totalEngagement / data.view_count : 0

      return {
        platform: 'tiktok',
        postId: data.id,
        postUrl: videoUrl,
        views: data.view_count,
        likes: data.like_count,
        comments: data.comment_count,
        shares: data.share_count,
        engagementRate,
        timestamp: new Date(data.create_time * 1000).toISOString(),
        username: data.username,
        accountInfo: {
          followerCount: 0, // Will be fetched separately
          accountAge: 0,
          verificationStatus: false
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch TikTok data')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Instagram API integration
  const fetchInstagramData = useCallback(async (
    accessToken: string,
    mediaUrl: string
  ): Promise<SocialMediaMetrics | null> => {
    try {
      setLoading(true)
      setError(null)

      // Extract media ID from URL
      const mediaId = extractInstagramMediaId(mediaUrl)
      if (!mediaId) {
        throw new Error('Invalid Instagram media URL')
      }

      // Fetch media data from Instagram Basic Display API
      const response = await fetch('/api/social/instagram/media', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${accessToken}`
        },
        body: JSON.stringify({ mediaId })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch Instagram data')
      }

      const data: InstagramMediaData = await response.json()

      // Calculate engagement rate (Instagram doesn't provide view count for posts)
      const totalEngagement = (data.like_count || 0) + (data.comments_count || 0)
      const engagementRate = 0.05 // Default estimate for Instagram posts

      return {
        platform: 'instagram',
        postId: data.id,
        postUrl: mediaUrl,
        views: totalEngagement * 20, // Estimate views based on engagement
        likes: data.like_count || 0,
        comments: data.comments_count || 0,
        shares: 0, // Instagram doesn't provide share count
        engagementRate,
        timestamp: data.timestamp,
        username: data.username,
        accountInfo: {
          followerCount: 0, // Will be fetched separately
          accountAge: 0,
          verificationStatus: false
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch Instagram data')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // YouTube API integration
  const fetchYouTubeData = useCallback(async (
    apiKey: string,
    videoUrl: string
  ): Promise<SocialMediaMetrics | null> => {
    try {
      setLoading(true)
      setError(null)

      // Extract video ID from URL
      const videoId = extractYouTubeVideoId(videoUrl)
      if (!videoId) {
        throw new Error('Invalid YouTube video URL')
      }

      // Fetch video data from YouTube Data API
      const response = await fetch('/api/social/youtube/video', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ videoId, apiKey })
      })

      if (!response.ok) {
        throw new Error('Failed to fetch YouTube data')
      }

      const data: YouTubeVideoData = await response.json()

      const views = parseInt(data.statistics.viewCount) || 0
      const likes = parseInt(data.statistics.likeCount) || 0
      const comments = parseInt(data.statistics.commentCount) || 0

      // Calculate engagement rate
      const totalEngagement = likes + comments
      const engagementRate = views > 0 ? totalEngagement / views : 0

      return {
        platform: 'youtube',
        postId: data.id,
        postUrl: videoUrl,
        views,
        likes,
        comments,
        shares: 0, // YouTube doesn't provide share count in basic API
        engagementRate,
        timestamp: data.snippet.publishedAt,
        username: data.snippet.channelTitle,
        accountInfo: {
          followerCount: 0, // Will be fetched separately
          accountAge: 0,
          verificationStatus: false
        }
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch YouTube data')
      return null
    } finally {
      setLoading(false)
    }
  }, [])

  // Generic function to fetch data from any platform
  const fetchSocialMediaData = useCallback(async (
    platform: 'tiktok' | 'instagram' | 'youtube',
    accessToken: string,
    postUrl: string
  ): Promise<SocialMediaMetrics | null> => {
    switch (platform) {
      case 'tiktok':
        return fetchTikTokData(accessToken, postUrl)
      case 'instagram':
        return fetchInstagramData(accessToken, postUrl)
      case 'youtube':
        return fetchYouTubeData(accessToken, postUrl)
      default:
        setError('Unsupported platform')
        return null
    }
  }, [fetchTikTokData, fetchInstagramData, fetchYouTubeData])

  return {
    loading,
    error,
    fetchTikTokData,
    fetchInstagramData,
    fetchYouTubeData,
    fetchSocialMediaData,
    clearError: () => setError(null)
  }
}

// Helper functions to extract IDs from URLs
function extractTikTokVideoId(url: string): string | null {
  const match = url.match(/\/video\/(\d+)/)
  return match ? match[1] : null
}

function extractInstagramMediaId(url: string): string | null {
  const match = url.match(/\/p\/([A-Za-z0-9_-]+)/)
  return match ? match[1] : null
}

function extractYouTubeVideoId(url: string): string | null {
  const match = url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/)([A-Za-z0-9_-]+)/)
  return match ? match[1] : null
}
