[06:03:37.328] Running build in Washington, D.C., USA (East) – iad1
[06:03:37.328] Build machine configuration: 2 cores, 8 GB
[06:03:37.375] Cloning github.com/rendoarsandi/creatorboost (Branch: feature/core-patch, Commit: ab1ae55)
[06:03:37.635] Previous build caches not available
[06:03:37.862] Cloning completed: 487.000ms
[06:03:38.215] Running "vercel build"
[06:03:38.655] Vercel CLI 43.3.0
[06:03:38.817] > Detected Turbo. Adjusting default settings...
[06:03:38.968] Running "install" command: `npm install`...
[06:03:41.827] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[06:03:42.227] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[06:03:42.316] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[06:03:43.607] npm warn deprecated @supabase/auth-helpers-shared@0.6.3: This package is now deprecated - please use the @supabase/ssr package instead.
[06:03:43.804] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[06:03:43.920] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[06:03:44.183] npm warn deprecated @supabase/auth-helpers-nextjs@0.8.7: This package is now deprecated - please use the @supabase/ssr package instead.
[06:03:46.482] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[06:03:58.057] 
[06:03:58.058] added 497 packages, and audited 502 packages in 19s
[06:03:58.058] 
[06:03:58.058] 140 packages are looking for funding
[06:03:58.059]   run `npm fund` for details
[06:03:58.059] 
[06:03:58.059] 2 low severity vulnerabilities
[06:03:58.059] 
[06:03:58.059] To address all issues (including breaking changes), run:
[06:03:58.059]   npm audit fix --force
[06:03:58.060] 
[06:03:58.060] Run `npm audit` for details.
[06:03:58.097] Detected Next.js version: 15.3.4
[06:03:58.098] Running "cd ../.. && npx turbo run build --filter=@creatorboost/admin"
[06:03:59.059] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[06:04:01.730] 
[06:04:01.731] Attention:
[06:04:01.731] Turborepo now collects completely anonymous telemetry regarding usage.
[06:04:01.731] This information is used to shape the Turborepo roadmap and prioritize features.
[06:04:01.731] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:04:01.731] https://turbo.build/repo/docs/telemetry
[06:04:01.731] 
[06:04:01.835] • Packages in scope: @creatorboost/admin
[06:04:01.836] • Running build in 1 packages
[06:04:01.836] • Remote caching enabled
[06:04:01.989] @creatorboost/admin:build: cache miss, executing 4ff064040a558e6c
[06:04:02.183] @creatorboost/admin:build: 
[06:04:02.184] @creatorboost/admin:build: > @creatorboost/admin@0.1.0 build
[06:04:02.184] @creatorboost/admin:build: > next build
[06:04:02.184] @creatorboost/admin:build: 
[06:04:03.374] @creatorboost/admin:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[06:04:03.375] @creatorboost/admin:build: This information is used to shape Next.js' roadmap and prioritize features.
[06:04:03.375] @creatorboost/admin:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[06:04:03.375] @creatorboost/admin:build: https://nextjs.org/telemetry
[06:04:03.375] @creatorboost/admin:build: 
[06:04:03.497] @creatorboost/admin:build:    ▲ Next.js 15.3.4
[06:04:03.498] @creatorboost/admin:build: 
[06:04:03.750] @creatorboost/admin:build:    Creating an optimized production build ...
[06:04:11.993] @creatorboost/admin:build: Failed to compile.
[06:04:11.994] @creatorboost/admin:build: 
[06:04:11.994] @creatorboost/admin:build: ./app/users/components/simple-user-table.tsx
[06:04:11.994] @creatorboost/admin:build: Module not found: Can't resolve '@creatorboost/hooks/use-admin-users'
[06:04:11.994] @creatorboost/admin:build: 
[06:04:11.994] @creatorboost/admin:build: https://nextjs.org/docs/messages/module-not-found
[06:04:11.994] @creatorboost/admin:build: 
[06:04:11.994] @creatorboost/admin:build: Import trace for requested module:
[06:04:11.995] @creatorboost/admin:build: ./app/users/page.tsx
[06:04:11.995] @creatorboost/admin:build: 
[06:04:12.010] @creatorboost/admin:build: 
[06:04:12.011] @creatorboost/admin:build: > Build failed because of webpack errors
[06:04:12.025] @creatorboost/admin:build: npm error Lifecycle script `build` failed with error:
[06:04:12.025] @creatorboost/admin:build: npm error code 1
[06:04:12.025] @creatorboost/admin:build: npm error path /vercel/path0/apps/admin
[06:04:12.025] @creatorboost/admin:build: npm error workspace @creatorboost/admin@0.1.0
[06:04:12.026] @creatorboost/admin:build: npm error location /vercel/path0/apps/admin
[06:04:12.026] @creatorboost/admin:build: npm error command failed
[06:04:12.026] @creatorboost/admin:build: npm error command sh -c next build
[06:04:12.036] 
[06:04:12.036]   Tasks:    0 successful, 1 total
[06:04:12.036]  Cached:    0 cached, 1 total
[06:04:12.036]    Time:    10.235s 
[06:04:12.037] Summary:    /vercel/path0/.turbo/runs/2zAbv8dBJh3MoZImLDCSnMRms2D.json
[06:04:12.037]  Failed:    @creatorboost/admin#build
[06:04:12.037] 
[06:04:12.037] @creatorboost/admin:build: ERROR: command finished with error: command (/vercel/path0/apps/admin) /node22/bin/npm run build exited (1)
[06:04:12.038] @creatorboost/admin#build: command (/vercel/path0/apps/admin) /node22/bin/npm run build exited (1)
[06:04:12.044]  ERROR  run failed: command  exited (1)
[06:04:12.138] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/admin" exited with 1
[06:04:12.469] 
[06:04:15.328] Exiting build container