[04:39:41.110] Running build in Washington, D.C., USA (East) – iad1
[04:39:41.110] Build machine configuration: 2 cores, 8 GB
[04:39:41.154] Cloning github.com/rendoarsandi/creatorboost (Branch: main, Commit: d6667a2)
[04:39:41.321] Previous build caches not available
[04:39:41.561] Cloning completed: 407.000ms
[04:39:41.907] Running "vercel build"
[04:39:42.371] Vercel CLI 43.3.0
[04:39:42.541] > Detected Turbo. Adjusting default settings...
[04:39:42.689] Running "install" command: `npm install`...
[04:39:45.509] npm warn deprecated rimraf@3.0.2: Rimraf versions prior to v4 are no longer supported
[04:39:46.104] npm warn deprecated inflight@1.0.6: This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.
[04:39:46.363] npm warn deprecated glob@7.2.3: Glob versions prior to v9 are no longer supported
[04:39:47.426] npm warn deprecated @supabase/auth-helpers-shared@0.6.3: This package is now deprecated - please use the @supabase/ssr package instead.
[04:39:47.576] npm warn deprecated @humanwhocodes/object-schema@2.0.3: Use @eslint/object-schema instead
[04:39:47.606] npm warn deprecated @humanwhocodes/config-array@0.13.0: Use @eslint/config-array instead
[04:39:47.976] npm warn deprecated @supabase/auth-helpers-nextjs@0.8.7: This package is now deprecated - please use the @supabase/ssr package instead.
[04:39:50.114] npm warn deprecated eslint@8.57.1: This version is no longer supported. Please see https://eslint.org/version-support for other options.
[04:40:01.263] 
[04:40:01.264] added 480 packages, and audited 485 packages in 18s
[04:40:01.264] 
[04:40:01.264] 140 packages are looking for funding
[04:40:01.265]   run `npm fund` for details
[04:40:01.265] 
[04:40:01.265] found 0 vulnerabilities
[04:40:01.327] Detected Next.js version: 15.3.4
[04:40:01.328] Running "cd ../.. && npx turbo run build --filter=@creatorboost/admin"
[04:40:02.737] npm warn exec The following package was not found and will be installed: turbo@2.5.4
[04:40:05.551] 
[04:40:05.551] Attention:
[04:40:05.552] Turborepo now collects completely anonymous telemetry regarding usage.
[04:40:05.552] This information is used to shape the Turborepo roadmap and prioritize features.
[04:40:05.552] You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:40:05.552] https://turbo.build/repo/docs/telemetry
[04:40:05.552] 
[04:40:05.595] • Packages in scope: @creatorboost/admin
[04:40:05.596] • Running build in 1 packages
[04:40:05.596] • Remote caching enabled
[04:40:05.717] @creatorboost/admin:build: cache miss, executing 9ca94f8e85196a19
[04:40:05.962] @creatorboost/admin:build: 
[04:40:05.963] @creatorboost/admin:build: > @creatorboost/admin@0.1.0 build
[04:40:05.963] @creatorboost/admin:build: > next build
[04:40:05.963] @creatorboost/admin:build: 
[04:40:07.204] @creatorboost/admin:build: Attention: Next.js now collects completely anonymous telemetry regarding usage.
[04:40:07.206] @creatorboost/admin:build: This information is used to shape Next.js' roadmap and prioritize features.
[04:40:07.206] @creatorboost/admin:build: You can learn more, including how to opt-out if you'd not like to participate in this anonymous program, by visiting the following URL:
[04:40:07.207] @creatorboost/admin:build: https://nextjs.org/telemetry
[04:40:07.207] @creatorboost/admin:build: 
[04:40:07.336] @creatorboost/admin:build:    ▲ Next.js 15.3.4
[04:40:07.337] @creatorboost/admin:build: 
[04:40:07.483] @creatorboost/admin:build:    Creating an optimized production build ...
[04:40:12.366] @creatorboost/admin:build: Failed to compile.
[04:40:12.367] @creatorboost/admin:build: 
[04:40:12.368] @creatorboost/admin:build: ../../packages/database/lib/supabase.ts
[04:40:12.368] @creatorboost/admin:build: Module not found: Can't resolve '@supabase/ssr'
[04:40:12.368] @creatorboost/admin:build: 
[04:40:12.368] @creatorboost/admin:build: https://nextjs.org/docs/messages/module-not-found
[04:40:12.368] @creatorboost/admin:build: 
[04:40:12.368] @creatorboost/admin:build: Import trace for requested module:
[04:40:12.368] @creatorboost/admin:build: ../../packages/database/index.ts
[04:40:12.368] @creatorboost/admin:build: ./app/components/user-nav.tsx
[04:40:12.368] @creatorboost/admin:build: 
[04:40:12.369] @creatorboost/admin:build: ../../packages/database/lib/supabase.ts
[04:40:12.369] @creatorboost/admin:build: Module not found: Can't resolve '@supabase/ssr'
[04:40:12.369] @creatorboost/admin:build: 
[04:40:12.369] @creatorboost/admin:build: https://nextjs.org/docs/messages/module-not-found
[04:40:12.370] @creatorboost/admin:build: 
[04:40:12.370] @creatorboost/admin:build: Import trace for requested module:
[04:40:12.370] @creatorboost/admin:build: ../../packages/database/index.ts
[04:40:12.370] @creatorboost/admin:build: ./app/page.tsx
[04:40:12.370] @creatorboost/admin:build: 
[04:40:12.386] @creatorboost/admin:build: 
[04:40:12.387] @creatorboost/admin:build: > Build failed because of webpack errors
[04:40:12.402] @creatorboost/admin:build: npm error Lifecycle script `build` failed with error:
[04:40:12.403] @creatorboost/admin:build: npm error code 1
[04:40:12.405] @creatorboost/admin:build: npm error path /vercel/path0/apps/admin
[04:40:12.405] @creatorboost/admin:build: npm error workspace @creatorboost/admin@0.1.0
[04:40:12.406] @creatorboost/admin:build: npm error location /vercel/path0/apps/admin
[04:40:12.406] @creatorboost/admin:build: npm error command failed
[04:40:12.406] @creatorboost/admin:build: npm error command sh -c next build
[04:40:12.410] @creatorboost/admin:build: ERROR: command finished with error: command (/vercel/path0/apps/admin) /node22/bin/npm run build exited (1)
[04:40:12.410] @creatorboost/admin#build: command (/vercel/path0/apps/admin) /node22/bin/npm run build exited (1)
[04:40:12.411] 
[04:40:12.411]   Tasks:    0 successful, 1 total
[04:40:12.411]  Cached:    0 cached, 1 total
[04:40:12.411]    Time:    6.852s 
[04:40:12.412] Summary:    /vercel/path0/.turbo/runs/2zARhq44Hgc3aXowA2wbH8Krdzp.json
[04:40:12.412]  Failed:    @creatorboost/admin#build
[04:40:12.412] 
[04:40:12.415]  ERROR  run failed: command  exited (1)
[04:40:12.497] Error: Command "cd ../.. && npx turbo run build --filter=@creatorboost/admin" exited with 1
[04:40:12.953] 
[04:40:16.605] Exiting build container