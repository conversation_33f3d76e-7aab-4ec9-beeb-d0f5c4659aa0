import { NextRequest, NextResponse } from 'next/server'

interface YouTubeVideoResponse {
  items: Array<{
    id: string
    snippet: {
      title: string
      description: string
      publishedAt: string
      channelTitle: string
      channelId: string
      thumbnails: {
        default: { url: string }
        medium: { url: string }
        high: { url: string }
      }
    }
    statistics: {
      viewCount: string
      likeCount: string
      commentCount: string
      favoriteCount: string
    }
  }>
}

interface YouTubeChannelResponse {
  items: Array<{
    id: string
    snippet: {
      title: string
      description: string
      publishedAt: string
      thumbnails: {
        default: { url: string }
        medium: { url: string }
        high: { url: string }
      }
    }
    statistics: {
      subscriberCount: string
      videoCount: string
      viewCount: string
    }
  }>
}

export async function POST(request: NextRequest) {
  try {
    const { videoId, apiKey } = await request.json()

    if (!videoId || !apiKey) {
      return NextResponse.json(
        { error: 'Video ID and API key are required' },
        { status: 400 }
      )
    }

    // Fetch video data from YouTube Data API v3
    const videoResponse = await fetch(
      `https://www.googleapis.com/youtube/v3/videos?id=${videoId}&part=snippet,statistics&key=${apiKey}`
    )

    if (!videoResponse.ok) {
      const errorData = await videoResponse.text()
      console.error('YouTube Video API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch video data from YouTube' },
        { status: videoResponse.status }
      )
    }

    const videoData: YouTubeVideoResponse = await videoResponse.json()

    if (!videoData.items || videoData.items.length === 0) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      )
    }

    const video = videoData.items[0]

    // Fetch channel data for additional context
    let channelData = null
    try {
      const channelResponse = await fetch(
        `https://www.googleapis.com/youtube/v3/channels?id=${video.snippet.channelId}&part=snippet,statistics&key=${apiKey}`
      )

      if (channelResponse.ok) {
        const channelResult: YouTubeChannelResponse = await channelResponse.json()
        if (channelResult.items && channelResult.items.length > 0) {
          channelData = channelResult.items[0]
        }
      }
    } catch (error) {
      console.warn('Failed to fetch YouTube channel data:', error)
      // Continue without channel data
    }

    // Return standardized video data
    return NextResponse.json({
      id: video.id,
      snippet: {
        title: video.snippet.title,
        description: video.snippet.description,
        publishedAt: video.snippet.publishedAt,
        channelTitle: video.snippet.channelTitle,
        channelId: video.snippet.channelId,
        thumbnails: video.snippet.thumbnails
      },
      statistics: {
        viewCount: video.statistics.viewCount,
        likeCount: video.statistics.likeCount,
        commentCount: video.statistics.commentCount,
        favoriteCount: video.statistics.favoriteCount
      },
      channel: channelData ? {
        id: channelData.id,
        title: channelData.snippet.title,
        description: channelData.snippet.description,
        publishedAt: channelData.snippet.publishedAt,
        subscriberCount: channelData.statistics.subscriberCount,
        videoCount: channelData.statistics.videoCount,
        totalViewCount: channelData.statistics.viewCount
      } : null
    })

  } catch (error) {
    console.error('YouTube API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Get channel videos
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const channelId = searchParams.get('channel_id')
    const apiKey = searchParams.get('api_key')
    const maxResults = searchParams.get('max_results') || '25'
    const order = searchParams.get('order') || 'date'

    if (!channelId || !apiKey) {
      return NextResponse.json(
        { error: 'Channel ID and API key are required' },
        { status: 400 }
      )
    }

    // Fetch channel's videos
    const response = await fetch(
      `https://www.googleapis.com/youtube/v3/search?channelId=${channelId}&part=snippet&type=video&order=${order}&maxResults=${maxResults}&key=${apiKey}`
    )

    if (!response.ok) {
      const errorData = await response.text()
      console.error('YouTube Search API Error:', errorData)
      
      return NextResponse.json(
        { error: 'Failed to fetch channel videos from YouTube' },
        { status: response.status }
      )
    }

    const data = await response.json()

    // Get detailed statistics for each video
    if (data.items && data.items.length > 0) {
      const videoIds = data.items.map((item: any) => item.id.videoId).join(',')
      
      try {
        const statsResponse = await fetch(
          `https://www.googleapis.com/youtube/v3/videos?id=${videoIds}&part=statistics&key=${apiKey}`
        )

        if (statsResponse.ok) {
          const statsData = await statsResponse.json()
          
          // Merge statistics with video data
          data.items = data.items.map((item: any) => {
            const stats = statsData.items?.find((stat: any) => stat.id === item.id.videoId)
            return {
              ...item,
              statistics: stats?.statistics || {}
            }
          })
        }
      } catch (error) {
        console.warn('Failed to fetch video statistics:', error)
        // Continue without detailed statistics
      }
    }

    return NextResponse.json(data)

  } catch (error) {
    console.error('YouTube Channel Videos API Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
