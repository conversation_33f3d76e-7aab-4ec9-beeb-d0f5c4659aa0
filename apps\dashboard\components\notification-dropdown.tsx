"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useNotifications } from '@creatorboost/hooks'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  NotificationBell,
  NotificationList,
  type NotificationData
} from '@creatorboost/ui'

export function NotificationDropdown() {
  const router = useRouter()
  const [isOpen, setIsOpen] = useState(false)
  
  const {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    clearError
  } = useNotifications()

  const handleNotificationAction = (notification: NotificationData) => {
    if (notification.action_url) {
      router.push(notification.action_url)
      setIsOpen(false)
      
      // Mark as read when user clicks action
      if (!notification.read) {
        markAsRead(notification.id)
      }
    }
  }

  const handleMarkAsRead = (id: string) => {
    markAsRead(id)
  }

  const handleMarkAllAsRead = () => {
    markAllAsRead()
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <NotificationBell 
          unreadCount={unreadCount}
          onClick={() => setIsOpen(!isOpen)}
        />
      </DropdownMenuTrigger>
      
      <DropdownMenuContent
        className="w-80 p-0"
        align="end"
      >
        <NotificationList
          notifications={notifications}
          onMarkAsRead={handleMarkAsRead}
          onMarkAllAsRead={handleMarkAllAsRead}
          onAction={handleNotificationAction}
          loading={loading}
        />
        
        {error && (
          <div className="p-3 border-t bg-destructive/10">
            <p className="text-sm text-destructive">{error}</p>
            <button 
              onClick={clearError}
              className="text-xs text-destructive hover:underline mt-1"
            >
              Dismiss
            </button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
