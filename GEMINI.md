# Project Overview

This is a Next.js monorepo managed with Turborepo. The project is structured into several applications: `landing`, `auth`, `dashboard`, and `admin`.

The backend is powered by Supabase, with the client configured in a shared `database` package and schema managed through SQL migrations in the `supabase` directory.

The UI is built with Tailwind CSS and shadcn/ui. Shared UI components are located in the `packages/ui` directory. A shared package for bot detection is also present.

## Applications

- **landing**: The main landing page for the application.
- **auth**: Handles user authentication, including a basic login page with Supabase Auth UI.
- **dashboard**: The main application dashboard for authenticated users.
- **admin**: The administration panel.

## Shared Packages

- **packages/database**: Contains the Supabase client configuration.
- **packages/ui**: Houses shared UI components built with shadcn/ui (e.g., button, icons).
- **packages/bot-detection**: Provides bot detection functionality.

## Technologies

- **Framework**: Next.js
- **Monorepo Tool**: Turborepo
- **Backend**: Supabase
- **Styling**: Tailwind CSS, shadcn/ui
- **Database Schema Management**: SQL migrations (Supabase)