{"name": "creatorboost-platform", "version": "0.1.0", "private": true, "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo run build", "clean": "turbo run clean", "db:generate": "cd packages/database && npm run generate", "db:push": "cd packages/database && npm run push", "deploy": "bash scripts/deploy.sh", "deploy:admin": "cd apps/admin && vercel --prod", "deploy:auth": "cd apps/auth && vercel --prod", "deploy:dashboard": "cd apps/dashboard && vercel --prod", "deploy:landing": "cd apps/landing && vercel --prod", "dev": "turbo run dev", "lint": "turbo run lint", "start": "turbo run start"}, "dependencies": {"@creatorboost/database": "workspace:*", "@creatorboost/ui": "workspace:*", "@emotion/is-prop-valid": "latest", "@hookform/resolvers": "latest", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@supabase/supabase-js": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "framer-motion": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "next": "^14.2.30", "next-themes": "latest", "react": "18.2.0", "react-day-picker": "latest", "react-dom": "18.2.0", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "latest", "zod": "latest"}, "devDependencies": {"@tailwindcss/postcss": "^4.1.11", "@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "postcss": "^8.5", "turbo": "^1.10.0", "typescript": "^5"}}