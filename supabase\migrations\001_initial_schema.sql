-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- <PERSON><PERSON> enum types
CREATE TYPE user_role AS ENUM ('creator', 'promoter', 'admin');
CREATE TYPE campaign_status AS ENUM ('active', 'paused', 'completed');
CREATE TYPE social_platform AS ENUM ('tiktok', 'instagram');
CREATE TYPE promotion_status AS ENUM ('active', 'under_review', 'approved', 'rejected');
CREATE TYPE payout_status AS ENUM ('pending', 'processing', 'completed', 'failed');

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email TEXT UNIQUE NOT NULL,
    role user_role NOT NULL DEFAULT 'promoter',
    profile JSONB DEFAULT '{}',
    wallet_balance DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Campaigns table
CREATE TABLE campaigns (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    creator_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title TEXT NOT NULL,
    description TEXT,
    budget_total DECIMAL(10,2) NOT NULL,
    budget_used DECIMAL(10,2) DEFAULT 0.00,
    pay_per_view DECIMAL(10,2) NOT NULL,
    requirements TEXT[] DEFAULT '{}',
    status campaign_status DEFAULT 'active',
    content_files JSONB DEFAULT '{}',
    max_views INTEGER GENERATED ALWAYS AS (FLOOR(budget_total / pay_per_view)) STORED,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Promotions table
CREATE TABLE promotions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    campaign_id UUID REFERENCES campaigns(id) ON DELETE CASCADE,
    promoter_id UUID REFERENCES users(id) ON DELETE CASCADE,
    social_platform social_platform NOT NULL,
    post_url TEXT NOT NULL,
    views_count INTEGER DEFAULT 0,
    earnings DECIMAL(10,2) DEFAULT 0.00,
    bot_score INTEGER DEFAULT 0,
    status promotion_status DEFAULT 'active',
    last_checked TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- View logs table for bot detection
CREATE TABLE view_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    promotion_id UUID REFERENCES promotions(id) ON DELETE CASCADE,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    view_count INTEGER NOT NULL,
    likes_count INTEGER DEFAULT 0,
    comments_count INTEGER DEFAULT 0,
    shares_count INTEGER DEFAULT 0,
    bot_indicators JSONB DEFAULT '{}',
    velocity_score INTEGER DEFAULT 0,
    engagement_score INTEGER DEFAULT 0
);

-- Payouts table
CREATE TABLE payouts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    status payout_status DEFAULT 'pending',
    transaction_id TEXT,
    processed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Platform settings table
CREATE TABLE platform_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key TEXT UNIQUE NOT NULL,
    value JSONB NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert default platform settings
INSERT INTO platform_settings (key, value) VALUES
('bot_detection_thresholds', '{"safe": 30, "warning": 60, "suspicious": 80, "bot": 100}'),
('platform_commission', '{"basic": 0.10, "pro": 0.08, "enterprise": 0.05}'),
('minimum_payout', '50000'),
('max_views_per_minute', '10');

-- Create indexes for better performance
CREATE INDEX idx_campaigns_creator_id ON campaigns(creator_id);
CREATE INDEX idx_campaigns_status ON campaigns(status);
CREATE INDEX idx_promotions_campaign_id ON promotions(campaign_id);
CREATE INDEX idx_promotions_promoter_id ON promotions(promoter_id);
CREATE INDEX idx_promotions_status ON promotions(status);
CREATE INDEX idx_view_logs_promotion_id ON view_logs(promotion_id);
CREATE INDEX idx_view_logs_timestamp ON view_logs(timestamp);
CREATE INDEX idx_payouts_user_id ON payouts(user_id);
CREATE INDEX idx_payouts_status ON payouts(status);

-- Row Level Security (RLS)
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE promotions ENABLE ROW LEVEL SECURITY;
ALTER TABLE view_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE payouts ENABLE ROW LEVEL SECURITY;

-- RLS Policies
-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

-- Campaigns policies
CREATE POLICY "Anyone can view active campaigns" ON campaigns
    FOR SELECT USING (status = 'active');

CREATE POLICY "Creators can manage own campaigns" ON campaigns
    FOR ALL USING (auth.uid() = creator_id);

-- Promotions policies
CREATE POLICY "Users can view own promotions" ON promotions
    FOR SELECT USING (auth.uid() = promoter_id);

CREATE POLICY "Promoters can create promotions" ON promotions
    FOR INSERT WITH CHECK (auth.uid() = promoter_id);

CREATE POLICY "Users can update own promotions" ON promotions
    FOR UPDATE USING (auth.uid() = promoter_id);

-- View logs policies (admin and system only)
CREATE POLICY "System can manage view logs" ON view_logs
    FOR ALL USING (true);

-- Payouts policies
CREATE POLICY "Users can view own payouts" ON payouts
    FOR SELECT USING (auth.uid() = user_id);

-- Functions for bot detection
CREATE OR REPLACE FUNCTION calculate_bot_score(
    promotion_uuid UUID
) RETURNS INTEGER AS $$
DECLARE
    velocity_score INTEGER := 0;
    engagement_score INTEGER := 0;
    pattern_score INTEGER := 0;
    final_score INTEGER := 0;
    recent_logs RECORD;
BEGIN
    -- Get recent view logs for this promotion
    SELECT 
        AVG(view_count) as avg_views,
        AVG(likes_count + comments_count) as avg_engagement,
        COUNT(*) as log_count
    INTO recent_logs
    FROM view_logs 
    WHERE promotion_id = promotion_uuid 
    AND timestamp > NOW() - INTERVAL '1 hour';
    
    -- Calculate velocity score (views per minute)
    IF recent_logs.avg_views > 10 THEN
        velocity_score := 100;
    ELSIF recent_logs.avg_views > 5 THEN
        velocity_score := 80;
    ELSIF recent_logs.avg_views > 3 THEN
        velocity_score := 60;
    ELSIF recent_logs.avg_views > 2 THEN
        velocity_score := 30;
    ELSE
        velocity_score := 0;
    END IF;
    
    -- Calculate engagement score
    IF recent_logs.avg_views > 0 THEN
        IF (recent_logs.avg_engagement / recent_logs.avg_views) < 0.01 THEN
            engagement_score := 80;
        ELSIF (recent_logs.avg_engagement / recent_logs.avg_views) < 0.02 THEN
            engagement_score := 60;
        ELSIF (recent_logs.avg_engagement / recent_logs.avg_views) < 0.05 THEN
            engagement_score := 30;
        ELSE
            engagement_score := 0;
        END IF;
    END IF;
    
    -- Calculate final weighted score
    final_score := (velocity_score * 0.6) + (engagement_score * 0.4);
    
    RETURN final_score;
END;
$$ LANGUAGE plpgsql;

-- Function to update promotion earnings
CREATE OR REPLACE FUNCTION update_promotion_earnings()
RETURNS TRIGGER AS $$
DECLARE
    campaign_pay_rate DECIMAL(10,2);
    new_earnings DECIMAL(10,2);
BEGIN
    -- Get the pay rate for this campaign
    SELECT pay_per_view INTO campaign_pay_rate
    FROM campaigns 
    WHERE id = NEW.campaign_id;
    
    -- Calculate new earnings
    new_earnings := NEW.views_count * campaign_pay_rate;
    
    -- Update the promotion earnings
    UPDATE promotions 
    SET earnings = new_earnings,
        updated_at = NOW()
    WHERE id = NEW.promotion_id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update earnings when view logs are inserted
CREATE TRIGGER update_earnings_trigger
    AFTER INSERT ON view_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_promotion_earnings();

-- Function to process daily payouts
CREATE OR REPLACE FUNCTION process_daily_payouts()
RETURNS INTEGER AS $$
DECLARE
    payout_count INTEGER := 0;
    promotion_record RECORD;
BEGIN
    -- Process approved promotions that haven't been paid out
    FOR promotion_record IN
        SELECT p.promoter_id, SUM(p.earnings) as total_earnings
        FROM promotions p
        WHERE p.status = 'approved'
        AND p.earnings > 0
        AND NOT EXISTS (
            SELECT 1 FROM payouts pay 
            WHERE pay.user_id = p.promoter_id 
            AND DATE(pay.created_at) = CURRENT_DATE
        )
        GROUP BY p.promoter_id
        HAVING SUM(p.earnings) >= 50000 -- Minimum payout threshold
    LOOP
        -- Create payout record
        INSERT INTO payouts (user_id, amount, status)
        VALUES (promotion_record.promoter_id, promotion_record.total_earnings, 'pending');
        
        -- Update user wallet balance
        UPDATE users 
        SET wallet_balance = wallet_balance + promotion_record.total_earnings
        WHERE id = promotion_record.promoter_id;
        
        payout_count := payout_count + 1;
    END LOOP;
    
    RETURN payout_count;
END;
$$ LANGUAGE plpgsql;
