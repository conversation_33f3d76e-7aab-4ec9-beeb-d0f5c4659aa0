{"name": "@creatorboost/ui", "version": "0.1.0", "main": "./index.ts", "types": "./index.ts", "exports": {".": "./index.ts", "./button": "./components/button.tsx", "./card": "./components/card.tsx", "./icons": "./components/icons.tsx", "./input": "./components/input.tsx", "./label": "./components/label.tsx", "./tabs": "./components/tabs.tsx", "./table": "./components/table.tsx", "./badge": "./components/badge.tsx", "./avatar": "./components/avatar.tsx", "./textarea": "./components/textarea.tsx", "./dialog": "./components/dialog.tsx", "./dropdown-menu": "./components/dropdown-menu.tsx", "./use-toast": "./components/use-toast.ts", "./toast": "./components/toast.tsx", "./toaster": "./components/toaster.tsx", "./theme-provider": "./components/theme-provider.tsx", "./utils": "./lib/utils.ts"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "next-themes": "^0.3.0", "react": "^18", "react-dom": "^18", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.4.0"}, "peerDependencies": {"react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/react": "^18.2.42", "@types/react-dom": "^18.2.17", "typescript": "^5.3.2"}}