// supabase/functions/metric-scraper/index.ts

import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from "https://esm.sh/@supabase/supabase-js@2"
import * as cheerio from "https://esm.sh/cheerio@1.0.0-rc.12"

const SUPABASE_URL = Deno.env.get("SUPABASE_URL")!
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY")!

const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY)

// --- Bot Detection Logic ---
function calculateBotScore(metrics: { views: number; likes: number; comments: number }, timeSinceSubmitMinutes: number): number {
  let botScore = 0;

  if (metrics.views === 0) return 0; // Avoid division by zero

  // 1. Engagement Rate (ER)
  const engagementRate = (metrics.likes + metrics.comments) / metrics.views;
  if (engagementRate < 0.005) { // Less than 0.5%
    botScore += 40; // High penalty for very low engagement
  }

  // 2. View Velocity (VV)
  if (timeSinceSubmitMinutes > 0) {
    const viewVelocity = metrics.views / timeSinceSubmitMinutes;
    if (viewVelocity > 500 && timeSinceSubmitMinutes < 60) { // > 500 views/min in the first hour
      botScore += 50; // High penalty for unnatural growth
    }
  }
  
  // 3. Like to View Ratio
  const likeToViewRatio = metrics.likes / metrics.views;
  if (likeToViewRatio < 0.001) { // Less than 0.1% likes to views
      botScore += 15;
  }

  return Math.min(botScore, 100); // Cap score at 100
}

async function getPostMetrics(postUrl: string) {
  try {
    const response = await fetch(postUrl, {
      headers: { "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36" }
    })
    if (!response.ok) throw new Error(`Failed to fetch post: ${response.statusText}`)
    
    const html = await response.text()
    const $ = cheerio.load(html)

    const views = $('meta[property="og:description"]').attr('content')?.split(' ')[0] || '0';
    const likes = $('strong[data-e2e="like-count"]').text() || '0';
    const comments = $('strong[data-e2e="comment-count"]').text() || '0';

    return {
      views: parseInt(views.replace(/,/g, ''), 10) || 0,
      likes: parseInt(likes.replace(/,/g, ''), 10) || 0,
      comments: parseInt(comments.replace(/,/g, ''), 10) || 0,
    }
  } catch (error) {
    console.error(`Error scraping ${postUrl}:`, error.message)
    return null
  }
}

serve(async (req) => {
  if (req.method !== 'POST') {
    return new Response("Method Not Allowed", { status: 405 })
  }

  try {
    const { submission_id, post_url, created_at } = await req.json()
    if (!submission_id || !post_url || !created_at) {
      return new Response("Missing submission_id, post_url, or created_at", { status: 400 })
    }

    const metrics = await getPostMetrics(post_url)
    if (!metrics) {
      return new Response("Failed to scrape metrics", { status: 500 })
    }

    // --- Calculate Bot Score ---
    const timeSinceSubmitMinutes = (new Date().getTime() - new Date(created_at).getTime()) / (1000 * 60);
    const botScore = calculateBotScore(metrics, timeSinceSubmitMinutes);

    // --- Save data to database ---
    const { error: metricsError } = await supabase.from("metrics").insert({ submission_id, ...metrics })
    if (metricsError) throw new Error(`Database (metrics) error: ${metricsError.message}`)

    const { error: submissionError } = await supabase
      .from("submissions")
      .update({ bot_score: botScore })
      .eq("id", submission_id)
    if (submissionError) throw new Error(`Database (submissions) error: ${submissionError.message}`)

    return new Response(JSON.stringify({ success: true, metrics, botScore }), {
      headers: { "Content-Type": "application/json" },
    })
  } catch (error) {
    return new Response(error.message, { status: 500 })
  }
})
