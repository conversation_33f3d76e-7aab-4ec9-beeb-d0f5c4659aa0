import Link from "next/link"
import { But<PERSON> } from "@creatorboost/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@creatorboost/ui/card";
import { Icons } from "@creatorboost/ui/icons";

function FeatureCard({ icon, title, description }: { icon: React.ReactNode, title: string, description: string }) {
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center space-x-4">
          <div className="p-2 bg-primary/10 rounded-md">{icon}</div>
          <CardTitle className="text-xl">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  )
}

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-screen">
      {/* Header */}
      <header className="container mx-auto h-16 flex items-center justify-between px-4">
        <Link href="/" className="flex items-center gap-2 font-bold text-xl">
          <span>CreatorBooster</span>
        </Link>
        <nav className="flex items-center gap-4">
          <Link href={process.env.NEXT_PUBLIC_AUTH_URL || "/login"}>
            <Button variant="ghost">Sign In</Button>
          </Link>
          <Link href={process.env.NEXT_PUBLIC_AUTH_URL || "/login"}>
            <Button>Get Started</Button>
          </Link>
        </nav>
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {/* Hero Section */}
        <section className="container mx-auto flex flex-col items-center justify-center text-center py-20 lg:py-32">
          <h1 className="text-4xl md:text-6xl font-bold tracking-tighter mb-4">
            Amplify Your Reach, Monetize Your Skills
          </h1>
          <p className="max-w-2xl text-lg text-gray-600 mb-8">
            CreatorBooster is the ultimate marketplace connecting content creators with talented promoters. Launch campaigns, drive views, and earn based on performance.
          </p>
          <div className="flex gap-4">
            <Link href={process.env.NEXT_PUBLIC_AUTH_URL || "/login"}>
              <Button size="lg" className="text-lg px-8">
                <Icons.user className="mr-2 h-5 w-5" />
                I'm a Creator
              </Button>
            </Link>
            <Link href={process.env.NEXT_PUBLIC_AUTH_URL || "/login"}>
              <Button size="lg" variant="outline" className="text-lg px-8">
                <Icons.users className="mr-2 h-5 w-5" />
                I'm a Promoter
              </Button>
            </Link>
          </div>
        </section>

        {/* Features Section */}
        <section className="bg-gray-50 py-20 lg:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">Why Choose CreatorBooster?</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <FeatureCard
                icon={<Icons.user className="h-6 w-6 text-primary" />}
                title="For Creators"
                description="Launch targeted campaigns with a set budget. Upload your content and let our network of promoters amplify your message across major platforms."
              />
              <FeatureCard
                icon={<Icons.dollarSign className="h-6 w-6 text-primary" />}
                title="For Promoters"
                description="Discover exciting campaigns, download materials, and use your creative skills to get paid for the views you generate. No fixed fees, just pure performance."
              />
              <FeatureCard
                icon={<Icons.check className="h-6 w-6 text-primary" />}
                title="Transparent & Fair"
                description="Our system tracks views reliably and calculates payouts automatically. Combined with bot detection, we ensure a fair ecosystem for everyone."
              />
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-20 lg:py-24">
          <div className="container mx-auto px-4">
            <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="text-center">
                <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-blue-600">1</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Create Campaign</h3>
                <p className="text-gray-600">Creators set up campaigns with budget, target audience, and content materials.</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-green-600">2</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Promoters Join</h3>
                <p className="text-gray-600">Skilled promoters discover campaigns and apply to participate in promotion activities.</p>
              </div>
              <div className="text-center">
                <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-2xl font-bold text-purple-600">3</span>
                </div>
                <h3 className="text-xl font-semibold mb-2">Get Paid</h3>
                <p className="text-gray-600">Earn money based on performance metrics and verified engagement results.</p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="bg-blue-600 text-white py-20 lg:py-24">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-3xl font-bold mb-4">Ready to Boost Your Content?</h2>
            <p className="text-xl mb-8 opacity-90">Join thousands of creators and promoters already using CreatorBooster</p>
            <Link href={process.env.NEXT_PUBLIC_AUTH_URL || "/login"}>
              <Button size="lg" variant="secondary" className="text-lg px-8">
                Get Started Today
              </Button>
            </Link>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="container mx-auto py-8 px-4">
        <div className="flex flex-col md:flex-row justify-between items-center text-center md:text-left">
          <p className="text-sm text-gray-600">&copy; {new Date().getFullYear()} CreatorBooster. All rights reserved.</p>
          <div className="flex gap-4 mt-4 md:mt-0">
            <Link href="/terms" className="text-sm text-gray-600 hover:text-gray-900 hover:underline">Terms of Service</Link>
            <Link href="/privacy" className="text-sm text-gray-600 hover:text-gray-900 hover:underline">Privacy Policy</Link>
          </div>
        </div>
      </footer>
    </div>
  )
}