-- Create custom types (enums) for status fields
CREATE TYPE campaign_status AS ENUM ('draft', 'active', 'paused', 'completed', 'archived');
CREATE TYPE submission_status AS ENUM ('pending', 'approved', 'rejected', 'processing_payment', 'paid');
CREATE TYPE payout_status AS ENUM ('pending', 'completed', 'failed');
CREATE TYPE user_role AS ENUM ('creator', 'promoter', 'admin');

-- Profiles Table
-- Extends the auth.users table with app-specific data
CREATE TABLE profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  role user_role NOT NULL DEFAULT 'promoter',
  full_name TEXT,
  tiktok_handle TEXT,
  instagram_handle TEXT,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Function to automatically create a profile when a new user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id, full_name)
  VALUES (new.id, new.raw_user_meta_data->>'full_name');
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to call the function after a new user is created
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();


-- Campaigns Table
-- Stores information about promotion campaigns created by content creators
CREATE TABLE campaigns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  creator_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  budget NUMERIC(10, 2) NOT NULL CHECK (budget > 0),
  cost_per_view NUMERIC(10, 4) NOT NULL CHECK (cost_per_view > 0),
  materials_url TEXT, -- Link to assets (e.g., Google Drive, Dropbox)
  status campaign_status NOT NULL DEFAULT 'draft',
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Submissions Table
-- Stores links submitted by promoters for a specific campaign
CREATE TABLE submissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  promoter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  campaign_id UUID NOT NULL REFERENCES campaigns(id) ON DELETE CASCADE,
  post_url TEXT NOT NULL UNIQUE,
  status submission_status NOT NULL DEFAULT 'pending',
  bot_score INTEGER DEFAULT 0,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Metrics Table
-- Stores time-series data for submitted posts (views, likes, etc.)
CREATE TABLE metrics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  submission_id UUID NOT NULL REFERENCES submissions(id) ON DELETE CASCADE,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  views BIGINT DEFAULT 0,
  likes BIGINT DEFAULT 0,
  comments BIGINT DEFAULT 0,
  CONSTRAINT unique_metric_per_submission_per_hour UNIQUE (submission_id, timestamp)
);

-- Payouts Table
-- Stores payout records for promoters
CREATE TABLE payouts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  promoter_id UUID NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
  amount NUMERIC(10, 2) NOT NULL,
  status payout_status NOT NULL DEFAULT 'pending',
  transaction_id TEXT, -- From payment gateway
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable Row Level Security (RLS) for all tables
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE campaigns ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE payouts ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Profiles
CREATE POLICY "Users can view their own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON profiles FOR UPDATE USING (auth.uid() = id);

-- RLS Policies for Campaigns
CREATE POLICY "Anyone can view active campaigns" ON campaigns FOR SELECT USING (status = 'active');
CREATE POLICY "Creators can manage their own campaigns" ON campaigns FOR ALL USING (auth.uid() = creator_id);

-- RLS Policies for Submissions
CREATE POLICY "Promoters can manage their own submissions" ON submissions FOR ALL USING (auth.uid() = promoter_id);
CREATE POLICY "Creators can view submissions for their campaigns" ON submissions FOR SELECT USING (
  EXISTS (
    SELECT 1 FROM campaigns WHERE campaigns.id = submissions.campaign_id AND campaigns.creator_id = auth.uid()
  )
);

-- RLS Policies for Payouts
CREATE POLICY "Promoters can view their own payouts" ON payouts FOR SELECT USING (auth.uid() = promoter_id);
