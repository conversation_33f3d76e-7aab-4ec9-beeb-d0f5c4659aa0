"use client"

import * as React from "react"
import { cn } from "../lib/utils"
import { <PERSON><PERSON> } from "./button"
import { Input } from "./input"
// import { ScrollArea } from "./scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "./avatar"
import { Icons } from "./icons"

interface Message {
  id: string
  content: string
  user_id: string
  user_name: string
  user_avatar?: string
  created_at: string
  message_type: 'text' | 'image' | 'file'
  file_url?: string
  file_name?: string
}

interface ChatProps {
  messages: Message[]
  currentUserId: string
  onSendMessage: (content: string, type?: 'text' | 'image' | 'file') => void
  onSendFile?: (file: File) => Promise<void>
  onLoadMore?: () => void
  isLoading?: boolean
  className?: string
}

const Chat = React.forwardRef<HTMLDivElement, ChatProps>(
  ({ messages, currentUserId, onSendMessage, onSendFile, onLoadMore, isLoading, className }, ref) => {
    const [newMessage, setNewMessage] = React.useState("")
    const [isTyping, setIsTyping] = React.useState(false)
    const [isUploading, setIsUploading] = React.useState(false)
    const messagesEndRef = React.useRef<HTMLDivElement>(null)
    const fileInputRef = React.useRef<HTMLInputElement>(null)

    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: "smooth" })
    }

    React.useEffect(() => {
      scrollToBottom()
    }, [messages])

    const handleSendMessage = (e: React.FormEvent) => {
      e.preventDefault()
      if (newMessage.trim()) {
        onSendMessage(newMessage.trim())
        setNewMessage("")
      }
    }

    const handleFileUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0]
      if (file && onSendFile) {
        try {
          setIsUploading(true)
          await onSendFile(file)
        } catch (error) {
          console.error('Failed to upload file:', error)
          // Fallback to text message if upload fails
          onSendMessage(`Failed to upload file: ${file.name}`, 'text')
        } finally {
          setIsUploading(false)
          // Reset file input
          if (fileInputRef.current) {
            fileInputRef.current.value = ''
          }
        }
      } else if (file) {
        // Fallback if onSendFile is not provided
        onSendMessage(`Uploaded file: ${file.name}`, 'file')
      }
    }

    const formatTime = (timestamp: string) => {
      return new Date(timestamp).toLocaleTimeString([], { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }

    const formatDate = (timestamp: string) => {
      const date = new Date(timestamp)
      const today = new Date()
      const yesterday = new Date(today)
      yesterday.setDate(yesterday.getDate() - 1)

      if (date.toDateString() === today.toDateString()) {
        return "Today"
      } else if (date.toDateString() === yesterday.toDateString()) {
        return "Yesterday"
      } else {
        return date.toLocaleDateString()
      }
    }

    const groupMessagesByDate = (messages: Message[]) => {
      const groups: { [key: string]: Message[] } = {}
      messages.forEach(message => {
        const date = new Date(message.created_at).toDateString()
        if (!groups[date]) {
          groups[date] = []
        }
        groups[date].push(message)
      })
      return groups
    }

    const messageGroups = groupMessagesByDate(messages)

    return (
      <div ref={ref} className={cn("flex flex-col h-full", className)}>
        <div className="flex-1 p-4 overflow-y-auto">
          {onLoadMore && (
            <div className="text-center mb-4">
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onLoadMore}
                disabled={isLoading}
              >
                {isLoading ? "Loading..." : "Load more messages"}
              </Button>
            </div>
          )}
          
          {Object.entries(messageGroups).map(([date, dateMessages]) => (
            <div key={date}>
              <div className="flex justify-center my-4">
                <span className="bg-muted px-3 py-1 rounded-full text-sm text-muted-foreground">
                  {formatDate(dateMessages[0].created_at)}
                </span>
              </div>
              
              {dateMessages.map((message) => {
                const isOwnMessage = message.user_id === currentUserId
                
                return (
                  <div
                    key={message.id}
                    className={cn(
                      "flex gap-3 mb-4",
                      isOwnMessage ? "flex-row-reverse" : "flex-row"
                    )}
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={message.user_avatar} />
                      <AvatarFallback>
                        {message.user_name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    
                    <div className={cn(
                      "flex flex-col max-w-[70%]",
                      isOwnMessage ? "items-end" : "items-start"
                    )}>
                      <div className={cn(
                        "rounded-lg px-3 py-2",
                        isOwnMessage 
                          ? "bg-primary text-primary-foreground" 
                          : "bg-muted"
                      )}>
                        {!isOwnMessage && (
                          <p className="text-xs font-medium mb-1 text-muted-foreground">
                            {message.user_name}
                          </p>
                        )}
                        
                        {message.message_type === 'text' && (
                          <p className="text-sm">{message.content}</p>
                        )}
                        
                        {message.message_type === 'file' && (
                          <div className="flex items-center gap-2">
                            <Icons.paperClip className="h-4 w-4" />
                            {message.file_url ? (
                              <a
                                href={message.file_url}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm underline hover:no-underline"
                              >
                                {message.file_name || message.content}
                              </a>
                            ) : (
                              <span className="text-sm">{message.file_name || message.content}</span>
                            )}
                          </div>
                        )}
                        
                        {message.message_type === 'image' && message.file_url && (
                          <div>
                            <img 
                              src={message.file_url} 
                              alt="Shared image" 
                              className="max-w-full h-auto rounded"
                            />
                            {message.content && (
                              <p className="text-sm mt-1">{message.content}</p>
                            )}
                          </div>
                        )}
                      </div>
                      
                      <span className="text-xs text-muted-foreground mt-1">
                        {formatTime(message.created_at)}
                      </span>
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
          
          <div ref={messagesEndRef} />
        </div>

        <div className="border-t p-4">
          <form onSubmit={handleSendMessage} className="flex gap-2">
            <Button
              type="button"
              variant="ghost"
              size="icon"
              onClick={() => fileInputRef.current?.click()}
              disabled={isLoading || isUploading}
            >
              {isUploading ? (
                <Icons.spinner className="h-4 w-4 animate-spin" />
              ) : (
                <Icons.paperClip className="h-4 w-4" />
              )}
            </Button>
            
            <Input
              value={newMessage}
              onChange={(e) => setNewMessage(e.target.value)}
              placeholder="Type a message..."
              className="flex-1"
              disabled={isLoading}
            />
            
            <Button type="submit" disabled={!newMessage.trim() || isLoading || isUploading}>
              <Icons.send className="h-4 w-4" />
            </Button>
          </form>
          
          <input
            ref={fileInputRef}
            type="file"
            className="hidden"
            onChange={handleFileUpload}
            accept="image/*,.pdf,.doc,.docx,.txt"
          />
        </div>
      </div>
    )
  }
)

Chat.displayName = "Chat"

export { Chat, type Message }
