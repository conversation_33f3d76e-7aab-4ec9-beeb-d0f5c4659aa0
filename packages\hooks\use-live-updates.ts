"use client"

import { useState, useEffect, useCallback } from 'react'
import { createSupabaseBrowserClient } from '@creatorboost/database'
import { useAuth } from './use-auth'
import { useNotifications } from './use-notifications'

export interface LiveUpdate {
  id: string
  type: 'campaign_status' | 'submission_status' | 'payment_status' | 'user_activity'
  entity_id: string
  entity_type: 'campaign' | 'submission' | 'payment' | 'user'
  old_data?: Record<string, any>
  new_data: Record<string, any>
  user_id: string
  created_at: string
  metadata?: Record<string, any>
}

export interface CampaignUpdate {
  id: string
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled'
  title: string
  updated_at: string
  updated_by?: string
}

export interface SubmissionUpdate {
  id: string
  status: 'pending' | 'approved' | 'rejected' | 'paid'
  campaign_id: string
  promoter_id: string
  updated_at: string
  updated_by?: string
}

export function useLiveUpdates() {
  const { user } = useAuth()
  const { sendNotification } = useNotifications()
  const [updates, setUpdates] = useState<LiveUpdate[]>([])
  const [campaignUpdates, setCampaignUpdates] = useState<CampaignUpdate[]>([])
  const [submissionUpdates, setSubmissionUpdates] = useState<SubmissionUpdate[]>([])
  const [isConnected, setIsConnected] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const supabase = createSupabaseBrowserClient()

  // Load recent updates
  const loadRecentUpdates = useCallback(async () => {
    if (!user) return

    try {
      const { data, error: fetchError } = await supabase
        .from('live_updates')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(20)

      if (fetchError) throw fetchError

      setUpdates(data || [])
    } catch (err) {
      console.error('Error loading recent updates:', err)
      setError(err instanceof Error ? err.message : 'Failed to load updates')
    }
  }, [user, supabase])

  // Handle campaign status changes
  const handleCampaignUpdate = useCallback(async (payload: any) => {
    const campaign = payload.new
    const oldCampaign = payload.old

    setCampaignUpdates(prev => {
      const existing = prev.find(c => c.id === campaign.id)
      if (existing) {
        return prev.map(c => c.id === campaign.id ? campaign : c)
      }
      return [campaign, ...prev.slice(0, 19)]
    })

    // Send notification for status changes
    if (oldCampaign?.status !== campaign.status && user) {
      const statusMessages = {
        active: 'Campaign is now active and accepting submissions',
        paused: 'Campaign has been paused',
        completed: 'Campaign has been completed',
        cancelled: 'Campaign has been cancelled'
      }

      const message = statusMessages[campaign.status as keyof typeof statusMessages]
      if (message) {
        try {
          await sendNotification(
            user.id,
            `Campaign Status Updated`,
            `${campaign.title}: ${message}`,
            'campaign',
            {
              action_url: `/campaigns/${campaign.id}`,
              action_text: 'View Campaign',
              campaign_id: campaign.id
            }
          )
        } catch (err) {
          console.error('Error sending campaign notification:', err)
        }
      }
    }
  }, [user, sendNotification])

  // Handle submission status changes
  const handleSubmissionUpdate = useCallback(async (payload: any) => {
    const submission = payload.new
    const oldSubmission = payload.old

    setSubmissionUpdates(prev => {
      const existing = prev.find(s => s.id === submission.id)
      if (existing) {
        return prev.map(s => s.id === submission.id ? submission : s)
      }
      return [submission, ...prev.slice(0, 19)]
    })

    // Send notification for status changes
    if (oldSubmission?.status !== submission.status && user) {
      const statusMessages = {
        approved: 'Your submission has been approved!',
        rejected: 'Your submission has been rejected',
        paid: 'Payment has been processed for your submission'
      }

      const message = statusMessages[submission.status as keyof typeof statusMessages]
      if (message) {
        try {
          await sendNotification(
            submission.promoter_id,
            `Submission Status Updated`,
            message,
            submission.status === 'paid' ? 'payment' : 'campaign',
            {
              action_url: `/campaigns/${submission.campaign_id}`,
              action_text: 'View Submission',
              campaign_id: submission.campaign_id
            }
          )
        } catch (err) {
          console.error('Error sending submission notification:', err)
        }
      }
    }
  }, [user, sendNotification])

  // Create live update record
  const createLiveUpdate = useCallback(async (
    type: LiveUpdate['type'],
    entityId: string,
    entityType: LiveUpdate['entity_type'],
    newData: Record<string, any>,
    oldData?: Record<string, any>,
    metadata?: Record<string, any>
  ) => {
    if (!user) return

    try {
      const { data, error: insertError } = await supabase
        .from('live_updates')
        .insert({
          type,
          entity_id: entityId,
          entity_type: entityType,
          old_data: oldData,
          new_data: newData,
          user_id: user.id,
          metadata,
          created_at: new Date().toISOString()
        })
        .select()
        .single()

      if (insertError) throw insertError

      return data
    } catch (err) {
      console.error('Error creating live update:', err)
      throw err
    }
  }, [user, supabase])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Set up real-time subscriptions
  useEffect(() => {
    if (!user) return

    loadRecentUpdates()

    const subscriptions = [
      // Campaign updates
      supabase
        .channel('campaigns-updates')
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'campaigns'
          },
          handleCampaignUpdate
        )
        .subscribe(),

      // Submission updates
      supabase
        .channel('submissions-updates')
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'submissions'
          },
          handleSubmissionUpdate
        )
        .subscribe(),

      // Live updates for current user
      supabase
        .channel(`live-updates-${user.id}`)
        .on(
          'postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'live_updates',
            filter: `user_id=eq.${user.id}`
          },
          (payload) => {
            const newUpdate = payload.new as LiveUpdate
            setUpdates(prev => [newUpdate, ...prev.slice(0, 19)])
          }
        )
        .subscribe()
    ]

    // Set initial connection status
    setIsConnected(true)

    return () => {
      subscriptions.forEach(sub => sub.unsubscribe())
    }
  }, [user, loadRecentUpdates, handleCampaignUpdate, handleSubmissionUpdate, supabase])

  return {
    updates,
    campaignUpdates,
    submissionUpdates,
    isConnected,
    error,
    createLiveUpdate,
    clearError,
    refresh: loadRecentUpdates
  }
}
