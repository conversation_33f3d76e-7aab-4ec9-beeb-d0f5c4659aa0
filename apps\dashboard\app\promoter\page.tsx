"use client"

import { useState, useEffect } from "react"
import { createSupabaseBrowserClient } from "@creatorboost/database"
import { Input } from "@creatorboost/ui/input"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@creatorboost/ui/card"
import { Button } from "@creatorboost/ui/button"
import { Badge } from "@creatorboost/ui/badge"
import { useToast } from "@creatorboost/ui/use-toast"

// Define the type for a campaign object
interface Campaign {
  id: string;
  name: string;
  description: string;
  cost_per_view: number;
  budget: number;
}

export default function PromoterDashboard() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const { toast } = useToast()
  const supabase = createSupabaseBrowserClient()

  useEffect(() => {
    const fetchCampaigns = async () => {
      setLoading(true)
      const { data, error } = await supabase
        .from("campaigns")
        .select("id, name, description, cost_per_view, budget")
        .eq("status", "active")

      if (error) {
        toast({ title: "Error", description: "Failed to fetch campaigns.", variant: "destructive" })
      } else {
        setCampaigns(data)
      }
      setLoading(false)
    }

    fetchCampaigns()
  }, [toast])

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Promoter Dashboard</h1>
        <p className="text-muted-foreground">Find campaigns and track your earnings.</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Available Campaigns</CardTitle>
          <CardDescription>Browse and join active campaigns to start earning</CardDescription>
        </CardHeader>
        <CardContent>
          <Input placeholder="Search for campaigns..." className="mb-4" />
        
          {loading ? (
            <p>Loading campaigns...</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {campaigns.length > 0 ? (
                campaigns.map(campaign => (
                  <Card key={campaign.id}>
                    <CardHeader>
                      <CardTitle className="text-lg">{campaign.name}</CardTitle>
                      <CardDescription>{campaign.description}</CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <Badge variant="secondary">${campaign.cost_per_view} / view</Badge>
                        <p className="text-sm text-muted-foreground">Budget: ${campaign.budget.toLocaleString()}</p>
                      </div>
                    </CardContent>
                    <CardFooter>
                      <Button className="w-full">View & Join</Button>
                    </CardFooter>
                  </Card>
                ))
              ) : (
                <p>No active campaigns found.</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>My Earnings</CardTitle>
          <CardDescription>Track your performance and earnings</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
            <div className="space-y-2">
              <p className="text-sm font-medium">Total Earnings</p>
              <p className="text-2xl font-bold">$0.00</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Active Campaigns</p>
              <p className="text-2xl font-bold">0</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Total Views</p>
              <p className="text-2xl font-bold">0</p>
            </div>
            <div className="space-y-2">
              <p className="text-sm font-medium">Conversion Rate</p>
              <p className="text-2xl font-bold">0%</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
